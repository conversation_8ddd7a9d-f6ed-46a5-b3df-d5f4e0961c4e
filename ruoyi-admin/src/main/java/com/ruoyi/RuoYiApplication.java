package com.ruoyi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@ComponentScan(basePackages = {"com.ruoyi", "com.mdl"})
public class RuoYiApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        ConfigurableApplicationContext context = SpringApplication.run(RuoYiApplication.class, args);
        System.out.println("系统启动成功！");
        
        // 打印所有Controller
        System.out.println("\n=== 已加载的Controller列表 ===");
        context.getBeansOfType(Object.class).forEach((name, bean) -> {
            if (bean.getClass().getName().contains("controller")) {
                System.out.println("Controller: " + bean.getClass().getName());
            }
        });
        System.out.println("========================\n");
    }
}