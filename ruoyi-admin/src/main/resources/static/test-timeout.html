<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajax超时测试页面</title>
    <script src="js/jquery.min.js"></script>
    <script src="ruoyi/js/common.js"></script>
    <script src="ruoyi/js/ry-ui.js"></script>
</head>
<body>
    <div style="padding: 20px;">
        <h2>Ajax超时测试页面</h2>
        <p>此页面用于测试Ajax请求的超时设置是否生效</p>
        
        <div style="margin: 20px 0;">
            <button id="testGlobalAjax" class="btn btn-primary">测试全局Ajax超时设置</button>
            <button id="testOperateSubmit" class="btn btn-success">测试$.operate.submit超时设置</button>
            <button id="testOperateSave" class="btn btn-info">测试$.operate.save超时设置</button>
        </div>
        
        <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9;">
            <h4>测试结果：</h4>
            <div id="resultContent">点击上方按钮开始测试...</div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 测试全局Ajax超时设置
            $('#testGlobalAjax').click(function() {
                $('#resultContent').html('正在测试全局Ajax超时设置...');
                
                $.ajax({
                    url: '/test/timeout', // 这个URL不存在，用于测试
                    type: 'POST',
                    data: { test: 'global' },
                    success: function(result) {
                        $('#resultContent').html('全局Ajax请求成功: ' + JSON.stringify(result));
                    },
                    error: function(xhr, status, error) {
                        var message = '全局Ajax请求失败: ';
                        if (status === 'timeout') {
                            message += '请求超时 (这说明超时设置生效了)';
                        } else {
                            message += 'Status: ' + status + ', Error: ' + error;
                        }
                        $('#resultContent').html(message);
                    }
                });
            });
            
            // 测试$.operate.submit超时设置
            $('#testOperateSubmit').click(function() {
                $('#resultContent').html('正在测试$.operate.submit超时设置...');
                
                // 模拟table配置
                if (!window.table) {
                    window.table = { options: { id: 'test' } };
                }
                
                $.operate.submit('/test/timeout', 'post', 'json', { test: 'submit' }, function(result) {
                    $('#resultContent').html('$.operate.submit请求回调: ' + JSON.stringify(result));
                });
            });
            
            // 测试$.operate.save超时设置
            $('#testOperateSave').click(function() {
                $('#resultContent').html('正在测试$.operate.save超时设置...');
                
                $.operate.save('/test/timeout', { test: 'save' }, function(result) {
                    $('#resultContent').html('$.operate.save请求回调: ' + JSON.stringify(result));
                });
            });
            
            // 监听Ajax错误事件
            $(document).ajaxError(function(event, xhr, settings, error) {
                if (xhr.status === 0 && error === 'timeout') {
                    $('#resultContent').append('<br><span style="color: green;">✓ 检测到超时事件，超时设置正常工作</span>');
                } else if (xhr.status === 404) {
                    $('#resultContent').append('<br><span style="color: orange;">⚠ 请求的URL不存在 (404)，这是正常的测试结果</span>');
                } else {
                    $('#resultContent').append('<br><span style="color: red;">✗ Ajax错误: Status=' + xhr.status + ', Error=' + error + '</span>');
                }
            });
            
            // 显示当前超时配置
            setTimeout(function() {
                var globalTimeout = $.ajaxSettings.timeout || '未设置';
                $('#resultContent').html('当前全局Ajax超时设置: ' + globalTimeout + ' 毫秒<br>点击上方按钮开始测试...');
            }, 100);
        });
    </script>
</body>
</html>
