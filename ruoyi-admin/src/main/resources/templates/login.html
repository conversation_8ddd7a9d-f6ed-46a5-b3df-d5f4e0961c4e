<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#64748B'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '2px',
                        DEFAULT: '4px',
                        'md': '8px',
                        'lg': '12px',
                        'xl': '16px',
                        '2xl': '20px',
                        '3xl': '24px',
                        'full': '9999px',
                        'button': '4px'
                    }
                }
            }
        }
    </script>
    <style>
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        .input-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 20px;
            height: 20px;
        }
        .captcha-container {
            background: linear-gradient(135deg, #f6f8fa 0%, #ebedff 100%);
            font-family: 'Courier New', monospace;
            user-select: none;
        }
    </style>
</head>
<body class="min-h-[1024px] bg-white flex items-center justify-center">
<div class="w-[400px] flex flex-col items-center">
    <div class="font-['Pacifico'] text-4xl mb-12 text-primary">logo</div>

    <h1 class="text-2xl font-medium text-gray-800 mb-8">账号登录</h1>

    <div class="w-full space-y-6">
        <form id="signupForm" autocomplete="off">
            <div class="relative">
                <div class="absolute left-4 top-1/2 -translate-y-1/2 input-icon text-gray-400">
                    <i class="fas fa-user"></i>
                </div>
                <input type="text" name="username" placeholder="请输入账号" class="w-full h-12 pl-12 pr-4 text-sm border border-gray-200 rounded-md focus:border-primary focus:outline-none transition-colors" />
            </div>

            <div class="relative">
                <div class="absolute left-4 top-1/2 -translate-y-1/2 input-icon text-gray-400">
                    <i class="fas fa-lock"></i>
                </div>
                <input type="password" name="password" placeholder="请输入密码" class="w-full h-12 pl-12 pr-4 text-sm border border-gray-200 rounded-md focus:border-primary focus:outline-none transition-colors" />
            </div>

            <div class="flex space-x-4">
                <div class="relative flex-1">
                    <div class="absolute left-4 top-1/2 -translate-y-1/2 input-icon text-gray-400">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <input type="text" name="validateCode" placeholder="请输入验证码" class="w-full h-12 pl-12 pr-4 text-sm border border-gray-200 rounded-md focus:border-primary focus:outline-none transition-colors" maxlength="4" />
                </div>
                <div class="w-[120px] h-12 rounded-md flex items-center justify-center cursor-pointer select-none">
                    <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode" width="85%" alt="验证码" onclick="this.src=this.src+'?'+Math.random()" style="cursor:pointer;"/>
                </div>
            </div>

            <button id="btnSubmit" class="w-full h-12 bg-primary text-white font-medium !rounded-button hover:bg-primary/90 active:bg-primary/80 transition-colors whitespace-nowrap">
                登录
            </button>
        </form>
    </div>
</div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.0}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
</body>
</html>