<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改包装BOM')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-bom-package-edit" th:object="${bomPackaging}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数据分析ID：</label>
                    <div class="col-sm-8">
                        <input name="dataAnalysisId" th:field="*{dataAnalysisId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">母件编码：</label>
                    <div class="col-sm-8">
                        <input name="parentCode" th:field="*{parentCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">子件编码：</label>
                    <div class="col-sm-8">
                        <input name="childCode" th:field="*{childCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">物料名称：</label>
                    <div class="col-sm-8">
                        <input name="childName" th:field="*{childName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">规格说明：</label>
                    <div class="col-sm-8">
                        <input name="specification" th:field="*{specification}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">子件数量：</label>
                    <div class="col-sm-8">
                        <input name="childQuantity" th:field="*{childQuantity}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">子件计量单位：</label>
                    <div class="col-sm-8">
                        <input name="childUnit" th:field="*{childUnit}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">订单量：</label>
                    <div class="col-sm-8">
                        <input name="orderQuantity" th:field="*{orderQuantity}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">物料需求小计：</label>
                    <div class="col-sm-8">
                        <input name="materialRequirementSubtotal" th:field="*{materialRequirementSubtotal}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/basedata/bom-package";
        $("#form-bom-package-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-bom-package-edit').serialize());
            }
        }
    </script>
</body>
</html>