<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改产成品对应的半成品')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-product-semifinished-edit" th:object="${productSemifinished}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数据分析ID：</label>
                    <div class="col-sm-8">
                        <input name="dataAnalysisId" th:field="*{dataAnalysisId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">半成品编码：</label>
                    <div class="col-sm-8">
                        <input name="neutralCode" th:field="*{neutralCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数量：</label>
                    <div class="col-sm-8">
                        <input name="quantity" th:field="*{quantity}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">车间仓结存：</label>
                    <div class="col-sm-8">
                        <input name="workshopInventory" th:field="*{workshopInventory}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">应生产：</label>
                    <div class="col-sm-8">
                        <input name="productionRequired" th:field="*{productionRequired}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/basedata/product-semifinished";
        $("#form-product-semifinished-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-product-semifinished-edit').serialize());
            }
        }
    </script>
</body>
</html>