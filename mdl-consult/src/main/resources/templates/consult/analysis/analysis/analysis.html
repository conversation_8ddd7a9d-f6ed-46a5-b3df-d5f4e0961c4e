<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('数据分析列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>统计编号：</label>
                                <input type="text" name="statisticsNumber"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="consult/analysis:analysis:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="consult/analysis:analysis:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="consult/analysis:analysis:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <!--<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="consult/analysis:analysis:export">
                    <i class="fa fa-download"></i> 导出
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('consult/analysis:analysis:edit')}]];
        var removeFlag = [[${@permission.hasPermi('consult/analysis:analysis:remove')}]];
        var prefix = ctx + "consult/analysis/analysis";

        // 打开欠料分析页面
        function materialShortageAnalysis(id) {
            var url = prefix + "/materialShortageAnalysis/" + id;
            $.modal.openTab("欠料分析", url);
        }

        // 打开销售对比页面
        function salesComparison(id) {
            var url = prefix + "/salesComparison/" + id;
            $.modal.openTab("销售对比", url);
        }

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "数据分析",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'statisticsNumber',
                    title: '统计编号',
                    formatter: function(value, row, index) {
                        return row.statisticsNumber + ' <span style="color: red;">' + row.taskStatus + '</span>';
                    }
                },
                {
                    field: 'createTime',
                    title: '统计日期',
                    formatter: function(value) {
                        if (!value) return '';
                        var date = new Date(value);
                        var y = date.getFullYear();
                        var m = (date.getMonth() + 1).toString().padStart(2, '0');
                        var d = date.getDate().toString().padStart(2, '0');
                        return y + '年' + m + '月' + d + '日';
                    }
                },

                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="materialShortageAnalysis(\'' + row.id + '\')"><i class="fa fa-bar-chart"></i>欠料分析</a> ');
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="salesComparison(\'' + row.id + '\')"><i class="fa fa-bar-chart"></i>销售与生产订单对比表</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-file-excel-o"></i>导入数据</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>