<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改数据分析')" />
    <style>
        .import-section {
            margin-top: 20px;
            border-top: 1px solid #e7eaec;
            padding-top: 15px;
        }
        .import-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .import-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .import-item-label {
            width: 200px;
            text-align: right;
            padding-right: 10px;
        }
        .import-btn {
            margin-right: 10px;
        }
        .btn-disabled {
            opacity: 0.65;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-analysis-edit" th:object="${dataAnalysis}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">统计编号：</label>
                    <div class="col-sm-8">
                        <input name="statisticsNumber" th:field="*{statisticsNumber}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>

            <!-- 数据导入 -->
            <div class="col-xs-12 import-section">
                <div class="import-title">导入基础档案</div>
                <div class="import-item">
                    <div class="import-item-label">BOM：</div>
                    <div id="bom-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-bom" onclick="importBom()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-bom" onclick="deleteBom()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">半成品BOM：</div>
                    <div id="bom-semifinished-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-bom-semifinished" onclick="importBomSemifinished()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-bom-semifinished" onclick="deleteBomSemifinished()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">包装BOM：</div>
                    <div id="bom-packaging-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-bom-packaging" onclick="importBomPackaging()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-bom-packaging" onclick="deleteBomPackaging()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">物料安全库存：</div>
                    <div id="safety-stock-material-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-safety-stock-material" onclick="importSafetyStockMaterial()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-safety-stock-material" onclick="deleteSafetyStockMaterial()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">产成品安全库存：</div>
                    <div id="safety-stock-finished-product-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-safety-stock-finished-product" onclick="importSafetyStockFinishedProduct()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-safety-stock-finished-product" onclick="deleteSafetyStockFinishedProduct()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">产成品对应半成品：</div>
                    <div id="product-semifinished-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-product-semifinished" onclick="importProductSemifinished()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-product-semifinished" onclick="deleteProductSemifinished()" disabled>删除</button>
                </div>
            </div>
            <div class="col-xs-12 import-section">
                <div class="import-title">导入业务数据</div>
                <div class="import-item">
                    <div class="import-item-label">未完成销售订单：</div>
                    <div id="sales-orders-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-sales-orders-incomplete" onclick="importSalesOrdersIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-sales-orders-incomplete" onclick="deleteSalesOrdersIncomplete()" disabled>删除</button>
                </div>
                <!--<div class="import-item">
                    <div class="import-item-label">需生产的销售单：</div>
                    <div id="sales-orders-for-production-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-sales-orders-for-production" onclick="importSalesOrdersForProduction()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-sales-orders-for-production" onclick="deleteSalesOrdersForProduction()" disabled>删除</button>
                </div>-->
                <div class="import-item">
                    <div class="import-item-label">未完成生产单：</div>
                    <div id="production-orders-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-production-orders-incomplete" onclick="importProductionOrdersIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-production-orders-incomplete" onclick="deleteProductionOrdersIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">未完成采购单：</div>
                    <div id="purchase-orders-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-purchase-orders-incomplete" onclick="importPurchaseOrdersIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-purchase-orders-incomplete" onclick="deletePurchaseOrdersIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">未完成委外订单：</div>
                    <div id="orders-outsourcing-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-orders-outsourcing-incomplete" onclick="importOrdersOutsourcingIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-orders-outsourcing-incomplete" onclick="deleteOrdersOutsourcingIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">已预购未下采购单：</div>
                    <div id="pre-purchased-not-ordered-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-pre-purchased-not-ordered" onclick="importPrePurchasedNotOrdered()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-pre-purchased-not-ordered" onclick="deletePrePurchasedNotOrdered()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">车间已领：</div>
                    <div id="materials-workshop-received-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-materials-workshop-received" onclick="importMaterialsWorkshopReceived()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-materials-workshop-received" onclick="deleteMaterialsWorkshopReceived()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">库存商品数：</div>
                    <div id="inventory-goods-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" id="btn-import-inventory-goods" onclick="importInventoryGoods()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-inventory-goods" onclick="deleteInventoryGoods()" disabled>删除</button>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/analysis/analysis";
        var bomPrefix = ctx + "consult/basedata/bom";
        var bomSemifinishedPrefix = ctx + "consult/basedata/bom-semifinished";
        var bomPackagingPrefix = ctx + "consult/basedata/bom-package";
        var safetyStockFinishedProductPrefix = ctx + "consult/basedata/safety-stock/finished-product";
        var safetyStockMaterialPrefix = ctx + "consult/basedata/safety-stock/material";
        var productSemifinishedPrefix = ctx + "consult/basedata/product-semifinished";
        var inventoryGoodsPrefix = ctx + "consult/bizdata/goods";
        var salesOrdersIncompletePrefix = ctx + "consult/bizdata/sales-orders/incomplete";
        var ordersOutsourcingIncompletePrefix = ctx + "consult/bizdata/purchase-orders/outsourcing-incomplete";
        var prePurchasedNotOrderedPrefix = ctx + "consult/bizdata/purchase-orders/pre-purchased";
        var salesOrdersForProductionPrefix = ctx + "consult/bizdata/sales-orders/production";
        var materialsWorkshopReceivedPrefix = ctx + "consult/bizdata/material/received";
        var purchaseOrdersIncompletePrefix = ctx + "consult/bizdata/purchase-orders/incomplete";
        var productionOrdersIncompletePrefix = ctx + "consult/bizdata/production-orders/incomplete";

        $(function() {
            // 初始化页面时检查各项数据是否已导入
            checkAllImportStatus();

            // 启动定时轮询检查导入中的任务
            startImportStatusPolling();
        });

        // 定时轮询检查导入中的任务状态
        function startImportStatusPolling() {
            setInterval(function() {
                console.log("1");
                var statisticsNumber = $("input[name='statisticsNumber']").val();
                if (!statisticsNumber) {
                    return;
                }
                console.log("2");
                // 检查是否有导入中的任务
                var importingTasks = [];
                $(".import-btn").each(function() {
                    if ($(this).text() === "导入中") {
                        var btnId = $(this).attr('id');
                        var dataType = getDataTypeFromBtnId(btnId);
                        if (dataType) {
                            importingTasks.push(dataType);
                        }
                    }
                });
                console.log("3");
                // 只轮询导入中的任务
                for (var i = 0; i < importingTasks.length; i++) {
                    checkImportTaskStatus(importingTasks[i], statisticsNumber);
                }
            }, 3000); // 每3秒检查一次
        }

        // 从按钮ID获取数据类型
        function getDataTypeFromBtnId(btnId) {
            if (!btnId || !btnId.startsWith('btn-import-')) {
                return null;
            }

            var id = btnId.replace('btn-import-', '');
            // 转换为驼峰命名
            return id.replace(/-([a-z])/g, function(match, letter) {
                return letter.toUpperCase();
            }).replace(/^([a-z])/, function(match, letter) {
                return letter.toUpperCase();
            });
        }

        $("#form-analysis-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-analysis-edit').serialize());
            }
        }

        // 检查所有数据导入状态
        function checkAllImportStatus() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                return;
            }

            // 检查基础数据
            checkImportTaskStatus("Bom", statisticsNumber);
            checkImportTaskStatus("BomSemifinished", statisticsNumber);
            checkImportTaskStatus("BomPackaging", statisticsNumber);
            checkImportTaskStatus("SafetyStockFinishedProduct", statisticsNumber);
            checkImportTaskStatus("SafetyStockMaterial", statisticsNumber);
            checkImportTaskStatus("ProductSemifinished", statisticsNumber);

            // 检查业务数据
            checkImportTaskStatus("InventoryGoods", statisticsNumber);
            checkImportTaskStatus("SalesOrdersIncomplete", statisticsNumber);
            checkImportTaskStatus("OrdersOutsourcingIncomplete", statisticsNumber);
            checkImportTaskStatus("PrePurchasedNotOrdered", statisticsNumber);
            checkImportTaskStatus("SalesOrdersForProduction", statisticsNumber);
            checkImportTaskStatus("MaterialsWorkshopReceived", statisticsNumber);
            checkImportTaskStatus("PurchaseOrdersIncomplete", statisticsNumber);
            checkImportTaskStatus("ProductionOrdersIncomplete", statisticsNumber);
        }

        // 检查导入任务状态
        function checkImportTaskStatus(dataType, statisticsNumber) {
            $.ajax({
                url: prefix + "/getImportTaskStatus",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber,
                    "importType": dataType
                },
                success: function(result) {
                    if (result.code == 0) {
                        // 使用统一的状态更新方法
                        updateImportStatus(dataType, result);
                    }
                }
            });
        }

        // 获取状态显示元素ID
        function getStatusId(dataType) {
            var id = dataType.replace(/([A-Z])/g, "-$1").toLowerCase();
            if (id.startsWith("-")) {
                id = id.substring(1);
            }
            return id + "-status";
        }

        // 获取删除按钮ID
        function getBtnId(dataType) {
            var id = dataType.replace(/([A-Z])/g, "-$1").toLowerCase();
            if (id.startsWith("-")) {
                id = id.substring(1);
            }
            return "btn-delete-" + id;
        }

        // 获取导入按钮ID
        function getImportBtnId(dataType) {
            var id = dataType.replace(/([A-Z])/g, "-$1").toLowerCase();
            if (id.startsWith("-")) {
                id = id.substring(1);
            }
            return "btn-import-" + id;
        }

        // 获取导入URL
        function getImportUrl(dataType) {
            switch(dataType) {
                case "Bom": return bomPrefix + "/importData";
                case "BomSemifinished": return bomSemifinishedPrefix + "/importData";
                case "BomPackaging": return bomPackagingPrefix + "/importData";
                case "SafetyStockFinishedProduct": return safetyStockFinishedProductPrefix + "/importData";
                case "SafetyStockMaterial": return safetyStockMaterialPrefix + "/importData";
                case "ProductSemifinished": return productSemifinishedPrefix + "/importData";
                case "InventoryGoods": return inventoryGoodsPrefix + "/importData";
                case "SalesOrdersIncomplete": return salesOrdersIncompletePrefix + "/importData";
                case "OrdersOutsourcingIncomplete": return ordersOutsourcingIncompletePrefix + "/importData";
                case "PrePurchasedNotOrdered": return prePurchasedNotOrderedPrefix + "/importData";
                case "SalesOrdersForProduction": return salesOrdersForProductionPrefix + "/importData";
                case "MaterialsWorkshopReceived": return materialsWorkshopReceivedPrefix + "/importData";
                case "PurchaseOrdersIncomplete": return purchaseOrdersIncompletePrefix + "/importData";
                case "ProductionOrdersIncomplete": return productionOrdersIncompletePrefix + "/importData";
                default: return "";
            }
        }

        // 获取模板URL
        function getTemplateUrl(dataType) {
            switch(dataType) {
                case "Bom": return bomPrefix + "/importTemplate";
                case "BomSemifinished": return bomSemifinishedPrefix + "/importTemplate";
                case "BomPackaging": return bomPackagingPrefix + "/importTemplate";
                case "SafetyStockFinishedProduct": return safetyStockFinishedProductPrefix + "/importTemplate";
                case "SafetyStockMaterial": return safetyStockMaterialPrefix + "/importTemplate";
                case "ProductSemifinished": return productSemifinishedPrefix + "/importTemplate";
                case "InventoryGoods": return inventoryGoodsPrefix + "/importTemplate";
                case "SalesOrdersIncomplete": return salesOrdersIncompletePrefix + "/importTemplate";
                case "OrdersOutsourcingIncomplete": return ordersOutsourcingIncompletePrefix + "/importTemplate";
                case "PrePurchasedNotOrdered": return prePurchasedNotOrderedPrefix + "/importTemplate";
                case "SalesOrdersForProduction": return salesOrdersForProductionPrefix + "/importTemplate";
                case "MaterialsWorkshopReceived": return materialsWorkshopReceivedPrefix + "/importTemplate";
                case "PurchaseOrdersIncomplete": return purchaseOrdersIncompletePrefix + "/importTemplate";
                case "ProductionOrdersIncomplete": return productionOrdersIncompletePrefix + "/importTemplate";
                default: return "";
            }
        }

        // 导入数据通用方法
        function importData(dataType, title) {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }
            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;
                        // 打开导入对话框
                        $.modal.open(title, getImportUrl(dataType) + "?dataAnalysisId=" + dataAnalysisId, '400', '300');

                        // 导入完成后刷新状态
                        setTimeout(function() {
                            checkImportTaskStatus(dataType, statisticsNumber);
                        }, 1000);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        // 各种导入方法
        function importBom() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("Bom", "导入BOM", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importBomSemifinished() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("BomSemifinished", "导入半成品BOM", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importBomPackaging() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("BomPackaging", "导入包装BOM", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSafetyStockFinishedProduct() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SafetyStockFinishedProduct", "导入产成品安全库存", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSafetyStockMaterial() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SafetyStockMaterial", "导入物料安全库存", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importProductSemifinished() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("ProductSemifinished", "导入产成品对应半成品", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importInventoryGoods() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("InventoryGoods", "导入库存商品数数据", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSalesOrdersIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SalesOrdersIncomplete", "导入未完成销售订单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importOrdersOutsourcingIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("OrdersOutsourcingIncomplete", "导入未完成委外订单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importPrePurchasedNotOrdered() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("PrePurchasedNotOrdered", "导入已预购未下采购单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSalesOrdersForProduction() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SalesOrdersForProduction", "导入需生产的销售单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importMaterialsWorkshopReceived() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("MaterialsWorkshopReceived", "导入车间已领", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importPurchaseOrdersIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("PurchaseOrdersIncomplete", "导入未完成采购单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importProductionOrdersIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("ProductionOrdersIncomplete", "导入未完成生产单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        // 删除数据通用方法
        function deleteData(dataType, title) {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            $.modal.confirm("确定要删除" + title + "数据吗？", function() {
                // 获取数据分析ID
                $.ajax({
                    url: prefix + "/list",
                    type: "post",
                    dataType: "json",
                    data: {
                        "statisticsNumber": statisticsNumber
                    },
                    success: function(result) {
                        if (result.rows && result.rows.length > 0) {
                            var dataAnalysisId = result.rows[0].id;

                            // 根据数据类型获取删除URL
                            var deleteUrl = getDeleteUrl(dataType);

                            // 执行删除操作
                            $.ajax({
                                url: deleteUrl,
                                type: "post",
                                dataType: "json",
                                data: {
                                    "dataAnalysisId": dataAnalysisId
                                },
                                success: function(result) {
                                    if (result.code == 0) {
                                        $.modal.msgSuccess("删除成功");
                                        // 刷新状态
                                        checkImportTaskStatus(dataType, statisticsNumber);
                                    } else {
                                        $.modal.alertError(result.msg);
                                    }
                                }
                            });
                        } else {
                            $.modal.alertWarning("未找到对应的数据分析记录");
                        }
                    }
                });
            });
        }

        // 获取删除URL
        function getDeleteUrl(dataType) {
            switch(dataType) {
                case "Bom": return bomPrefix + "/deleteByDataAnalysisId";
                case "BomSemifinished": return bomSemifinishedPrefix + "/deleteByDataAnalysisId";
                case "BomPackaging": return bomPackagingPrefix + "/deleteByDataAnalysisId";
                case "SafetyStockFinishedProduct": return safetyStockFinishedProductPrefix + "/deleteByDataAnalysisId";
                case "SafetyStockMaterial": return safetyStockMaterialPrefix + "/deleteByDataAnalysisId";
                case "ProductSemifinished": return productSemifinishedPrefix + "/deleteByDataAnalysisId";
                case "InventoryGoods": return inventoryGoodsPrefix + "/deleteByDataAnalysisId";
                case "SalesOrdersIncomplete": return salesOrdersIncompletePrefix + "/deleteByDataAnalysisId";
                case "OrdersOutsourcingIncomplete": return ordersOutsourcingIncompletePrefix + "/deleteByDataAnalysisId";
                case "PrePurchasedNotOrdered": return prePurchasedNotOrderedPrefix + "/deleteByDataAnalysisId";
                case "SalesOrdersForProduction": return salesOrdersForProductionPrefix + "/deleteByDataAnalysisId";
                case "MaterialsWorkshopReceived": return materialsWorkshopReceivedPrefix + "/deleteByDataAnalysisId";
                case "PurchaseOrdersIncomplete": return purchaseOrdersIncompletePrefix + "/deleteByDataAnalysisId";
                case "ProductionOrdersIncomplete": return productionOrdersIncompletePrefix + "/deleteByDataAnalysisId";
                default: return "";
            }
        }

        // 各种删除方法
        function deleteBom() {
            deleteData("Bom", "BOM");
        }

        function deleteBomSemifinished() {
            deleteData("BomSemifinished", "半成品BOM");
        }

        function deleteBomPackaging() {
            deleteData("BomPackaging", "包装BOM");
        }

        function deleteSafetyStockFinishedProduct() {
            deleteData("SafetyStockFinishedProduct", "产成品安全库存");
        }

        function deleteSafetyStockMaterial() {
            deleteData("SafetyStockMaterial", "物料安全库存");
        }

        function deleteProductSemifinished() {
            deleteData("ProductSemifinished", "产成品对应半成品");
        }

        function deleteInventoryGoods() {
            deleteData("InventoryGoods", "库存商品数");
        }

        function deleteSalesOrdersIncomplete() {
            deleteData("SalesOrdersIncomplete", "未完成销售订单");
        }

        function deleteOrdersOutsourcingIncomplete() {
            deleteData("OrdersOutsourcingIncomplete", "未完成委外订单");
        }

        function deletePrePurchasedNotOrdered() {
            deleteData("PrePurchasedNotOrdered", "已预购未下采购单");
        }

        function deleteSalesOrdersForProduction() {
            deleteData("SalesOrdersForProduction", "需生产的销售单");
        }

        function deleteMaterialsWorkshopReceived() {
            deleteData("MaterialsWorkshopReceived", "车间已领");
        }

        function deletePurchaseOrdersIncomplete() {
            deleteData("PurchaseOrdersIncomplete", "未完成采购单");
        }

        function deleteProductionOrdersIncomplete() {
            deleteData("ProductionOrdersIncomplete", "未完成生产单");
        }

        // 打开自定义导入对话框
        function openImportDialog(dataType, title, dataAnalysisId) {
            // 创建对话框内容
            var dialogContent =
                '<div class="import-dialog">' +
                '    <div class="form-group">' +
                '        <div class="file-input-container">' +
                '            <button type="button" class="btn btn-default" id="chooseFileBtn">选择文件</button>' +
                '            <span id="fileNameDisplay">未选择文件</span>' +
                '            <input type="file" id="importFile" name="file" style="display: none;" accept=".xls,.xlsx"/>' +
                '        </div>' +
                '    </div>' +
                '    <div class="form-group" style="margin-top: 15px;">' +
                '        <div class="checkbox">' +
                '            <label>' +
                '                <input type="checkbox" id="updateSupportCheckbox"> 是否更新已经存在的数据' +
                '            </label>' +
                '        </div>' +
                '        <a class="btn btn-default btn-xs" onclick="downloadTemplate(\'' + dataType + '\')">' +
                '            <i class="fa fa-download"></i> 下载模板' +
                '        </a>' +
                '    </div>' +
                '    <div class="form-group" style="margin-top: 10px; color: red;">' +
                '        提示：仅允许导入"xls"或"xlsx"格式文件！' +
                '    </div>' +
                '    <div class="form-group text-center" style="margin-top: 20px;">' +
                '        <button type="button" class="btn btn-primary" onclick="submitImport(\'' + dataType + '\', ' + dataAnalysisId + ')">导入</button>' +
                '        <button type="button" class="btn btn-default" style="margin-left: 10px;" onclick="closeImportDialog()">取消</button>' +
                '    </div>' +
                '</div>';

            // 打开对话框
            layer.open({
                type: 1,
                title: title,
                closeBtn: 1,
                area: ['400px', '300px'],
                content: dialogContent,
                success: function(layero, index) {
                    // 存储对话框索引
                    window.importDialogIndex = index;

                    // 设置文件选择按钮事件
                    $('#chooseFileBtn').on('click', function() {
                        $('#importFile').click();
                    });

                    // 文件选择后显示文件名
                    $('#importFile').on('change', function() {
                        var fileName = $(this).val().split('\\').pop();
                        if (fileName) {
                            $('#fileNameDisplay').text(fileName);
                        } else {
                            $('#fileNameDisplay').text('未选择文件');
                        }
                    });
                }
            });
        }

        // 下载模板
        function downloadTemplate(dataType) {
            var templateUrl = getTemplateUrl(dataType);
            if (templateUrl) {
                // 显示下载中的加载提示
                var loadingIndex = layer.msg('正在获取模板，请稍候......', {
                    icon: 16,
                    shade: 0.3,
                    time: 0 // 不自动关闭
                });

                // 先调用模板API获取文件名
                $.ajax({
                    url: templateUrl,
                    type: "get",
                    dataType: "json",
                    success: function(result) {
                        // 关闭加载提示
                        layer.close(loadingIndex);

                        if (result.code == 0 && result.msg) {
                            // 获取文件名成功，调用下载API
                            var fileName = result.msg;
                            var downloadUrl = ctx + "common/download?fileName=" + encodeURIComponent(fileName) + "&delete=true";
                            window.location.href = downloadUrl;
                        } else {
                            $.modal.alertError("获取模板文件失败：" + result.msg);
                        }
                    },
                    error: function() {
                        // 关闭加载提示
                        layer.close(loadingIndex);
                        $.modal.alertError("获取模板文件失败，请稍后重试");
                    }
                });
            }
        }

        // 关闭导入对话框
        function closeImportDialog() {
            if (window.importDialogIndex !== undefined) {
                layer.close(window.importDialogIndex);
            }
        }

        // 从表格导入对话框下载模板
        function downloadTemplateFromTable() {
            // 获取当前导入的模板URL
            var templateUrl = table.options.importTemplateUrl;
            if (templateUrl) {
                // 显示下载中的加载提示
                var loadingIndex = layer.msg('正在获取模板，请稍候......', {
                    icon: 16,
                    shade: 0.3,
                    time: 0 // 不自动关闭
                });

                // 先调用模板API获取文件名
                $.ajax({
                    url: templateUrl,
                    type: "get",
                    dataType: "json",
                    success: function(result) {
                        // 关闭加载提示
                        layer.close(loadingIndex);

                        if (result.code == 0 && result.msg) {
                            // 获取文件名成功，调用下载API
                            var fileName = result.msg;
                            var downloadUrl = ctx + "common/download?fileName=" + encodeURIComponent(fileName) + "&delete=true";
                            window.location.href = downloadUrl;
                        } else {
                            $.modal.alertError("获取模板文件失败：" + result.msg);
                        }
                    },
                    error: function() {
                        // 关闭加载提示
                        layer.close(loadingIndex);
                        $.modal.alertError("获取模板文件失败，请稍后重试");
                    }
                });
            }
        }

        // 提交导入
        function submitImport(dataType, dataAnalysisId) {
            var fileInput = $('#importFile')[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                $.modal.alertWarning("请选择要导入的文件");
                return;
            }

            var file = fileInput.files[0];
            var fileName = file.name;
            if (!fileName.endsWith('.xls') && !fileName.endsWith('.xlsx')) {
                $.modal.alertWarning("文件格式不正确，请选择 .xls 或 .xlsx 格式的文件");
                return;
            }

            var statisticsNumber = $("input[name='statisticsNumber']").val();
            var updateSupport = $('#updateSupportCheckbox').prop('checked');

            // 检查当前任务状态，只有等待中、失败状态才允许提交
            $.ajax({
                url: prefix + "/getImportTaskStatus",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber,
                    "importType": dataType
                },
                success: function(result) {
                    if (result.code == 0) {
                        var status = result.status;

                        // 检查是否允许提交导入申请
                        if (status === "1") { // 导入中
                            $.modal.alertWarning("当前有导入任务正在进行中，请稍后再试");
                            return;
                        }

                        // 允许提交导入申请，调用异步导入接口
                        submitAsyncImport(dataType, statisticsNumber, file, updateSupport);
                    } else {
                        // 如果查询失败，也允许提交（可能是首次导入）
                        submitAsyncImport(dataType, statisticsNumber, file, updateSupport);
                    }
                },
                error: function() {
                    // 如果查询失败，也允许提交（可能是首次导入）
                    submitAsyncImport(dataType, statisticsNumber, file, updateSupport);
                }
            });
        }

        // 提交异步导入
        function submitAsyncImport(dataType, statisticsNumber, file, updateSupport) {
            var formData = new FormData();
            formData.append('file', file);
            formData.append('statisticsNumber', statisticsNumber);
            formData.append('importType', dataType);
            formData.append('updateSupport', updateSupport);

            // 显示导入中的加载提示
            var loadingIndex = layer.msg('正在提交导入任务......', {
                icon: 16,
                shade: 0.3,
                time: 0 // 不自动关闭
            });

            // 更新按钮状态为导入中
            var importBtnId = getImportBtnId(dataType);
            $("#" + importBtnId).prop("disabled", true).text("导入中");

            $.ajax({
                url: prefix + "/asyncImport",
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function(result) {
                    // 关闭加载提示
                    layer.close(loadingIndex);

                    if (result.code == 0) {
                        $.modal.alertSuccess("导入任务已提交，任务ID：" + result.taskId);
                        closeImportDialog();

                        // 立即刷新导入状态
                        checkImportTaskStatus(dataType, statisticsNumber);

                        // 开始轮询任务状态
                        startTaskPolling(dataType, statisticsNumber);
                    } else {
                        $.modal.alertError(result.msg);
                        // 恢复按钮状态
                        $("#" + importBtnId).prop("disabled", false).text("导入");
                    }
                },
                error: function() {
                    // 关闭加载提示
                    layer.close(loadingIndex);
                    $.modal.alertError("提交导入任务失败，请重试");
                    // 恢复按钮状态
                    $("#" + importBtnId).prop("disabled", false).text("导入");
                }
            });
        }

        // 开始轮询特定任务状态
        function startTaskPolling(dataType, statisticsNumber) {
            var pollInterval = setInterval(function() {
                $.ajax({
                    url: prefix + "/getImportTaskStatus",
                    type: "post",
                    dataType: "json",
                    data: {
                        "statisticsNumber": statisticsNumber,
                        "importType": dataType
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            var status = result.status;

                            // 更新状态显示
                            updateImportStatus(dataType, result);

                            // 如果任务完成或失败，停止轮询
                            if (status === "2" || status === "3") {
                                clearInterval(pollInterval);

                                // 显示完成消息
                                if (status === "2") {
                                    $.modal.msgSuccess("导入完成：" + result.message);
                                } else if (status === "3") {
                                    $.modal.msgError("导入失败：" + result.message);
                                }
                            }
                        }
                    }
                });
            }, 2000); // 每2秒轮询一次
        }

        // 更新导入状态显示
        function updateImportStatus(dataType, result) {
            var statusId = getStatusId(dataType);
            var btnId = getBtnId(dataType);
            var importBtnId = getImportBtnId(dataType);

            var status = result.status;
            var message = result.message;

            // 更新状态显示
            $("#" + statusId).text(message || "未导入");

            // 根据状态更新按钮状态
            if (status === "1") { // 导入中
                $("#" + importBtnId).prop("disabled", true).text("导入中");
                $("#" + btnId).prop("disabled", true);
            } else if (status === "2") { // 已完成
                $("#" + importBtnId).prop("disabled", false).text("导入");
                $("#" + btnId).prop("disabled", false);
            } else if (status === "3") { // 失败
                $("#" + importBtnId).prop("disabled", false).text("导入");
                $("#" + btnId).prop("disabled", true);
            } else { // 未导入或其他状态
                $("#" + importBtnId).prop("disabled", false).text("导入");
                $("#" + btnId).prop("disabled", true);
            }
        }
    </script>

    <style>
        .import-dialog {
            padding: 20px;
        }
        .file-input-container {
            display: flex;
            align-items: center;
        }
        #fileNameDisplay {
            margin-left: 10px;
            color: #666;
        }
        .layui-layer-title {
            background-color: #f8f8f8;
            border-bottom: 1px solid #eee;
        }
        .btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    </style>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果数据已经存在，更新这条数据。"> 是否更新已经存在的数据
             &nbsp;	<a onclick="downloadTemplateFromTable()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>