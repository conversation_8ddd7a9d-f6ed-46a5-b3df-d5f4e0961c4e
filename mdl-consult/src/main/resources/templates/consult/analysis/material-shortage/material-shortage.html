<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('欠料分析')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label for="statisticsNumber">统计编号：</label>
                                <input type="text" id="statisticsNumber" name="statisticsNumber" th:value="${statisticsNumber}" readonly/>
                            </li>
                            <li>
                                <label for="childCode">子件编码：</label>
                                <input type="text" id="childCode" name="childCode"/>
                            </li>
                            <li>
                                <label for="materialName">物料名称：</label>
                                <input type="text" id="materialName" name="materialName"/>
                            </li>
                            <li>
                                <button type="button" class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</button>
                                <button type="button" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</button>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <button type="button" class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="analysis:material-shortage:export">
                    <i class="fa fa-download"></i> 导出
                </button>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/analysis/material-shortage";
        var statisticsNumber = [[${statisticsNumber}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "欠料分析",
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    search.statisticsNumber = statisticsNumber;
                    return search;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'childCode',
                    title: '子件编码'
                },
                {
                    field: 'materialName',
                    title: '物料名称'
                },
                {
                    field: 'specification',
                    title: '规格说明'
                },
                {
                    field: 'unit',
                    title: '单位'
                },
                {
                    field: 'semifinishedMaterialRequirement',
                    title: '半成品物料需求小计'
                },
                {
                    field: 'packagingMaterialRequirement',
                    title: '包装物料需求小计'
                },
                {
                    field: 'orderDemand',
                    title: '订单需求'
                },
                {
                    field: 'inventoryMaterial',
                    title: '库存物料'
                },
                {
                    field: 'purchaseUnreturned',
                    title: '采购未回'
                },
                {
                    field: 'outsourcingUnreturned',
                    title: '委外未回'
                },
                {
                    field: 'workshopMaterial',
                    title: '车间物料'
                },
                {
                    field: 'prePurchasedNotPurchased',
                    title: '已预购未采购'
                },
                {
                    field: 'safetyStock',
                    title: '安全库存'
                },
                {
                    field: 'shortage',
                    title: '欠料'
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
