<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('销售对比')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label for="statisticsNumber">统计编号：</label>
                                <input type="text" id="statisticsNumber" name="statisticsNumber" th:value="${statisticsNumber}" readonly/>
                            </li>
                            <li>
                                <label for="orderNo">订单号：</label>
                                <input type="text" id="orderNo" name="orderNo"/>
                            </li>
                            <li>
                                <label for="inventoryCode">存货编码：</label>
                                <input type="text" id="inventoryCode" name="inventoryCode"/>
                            </li>
                            <li>
                                <button type="button" class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</button>
                                <button type="button" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</button>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <button type="button" class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="analysis:sales-comparison:export">
                    <i class="fa fa-download"></i> 导出
                </button>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table">
                    <thead>
                        <tr>
                            <th data-checkbox="true"></th>
                            <th data-field="id" data-visible="false"></th>
                            <th data-field="orderDate" data-formatter="dateFormatter">订单日期</th>
                            <th data-field="orderNo">单号</th>
                            <th data-field="productionOrderNo">生产单号</th>
                            <th data-field="customerName">客户名称</th>
                            <th data-field="inventoryName">存货名称</th>
                            <th data-field="specification">规格型号</th>
                            <th data-field="unit">单位</th>
                            <th data-field="quantity">数量</th>
                            <th data-field="cumulativeShipment">累计发货数量</th>
                            <th data-field="unshippedQuantity">未发货数量</th>
                            <th data-field="currentInventory">减现有：库存</th>
                            <th data-field="productionNeeded">需要生产数</th>
                            <th data-field="actualInProduction">实际在产数</th>
                            <th data-field="difference">差异数</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/analysis/sales-comparison";
        var statisticsNumber = [[${statisticsNumber}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "销售对比",
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    search.statisticsNumber = statisticsNumber;
                    return search;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'orderDate',
                    title: '订单日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: 'orderNo',
                    title: '单号'
                },
                    {
                        field: 'inventoryCode',
                        title: '存货编码'
                    },
                {
                    field: 'productionOrderNo',
                    title: '生产单号'
                },
                {
                    field: 'customerName',
                    title: '客户名称'
                },
                {
                    field: 'inventoryName',
                    title: '存货名称'
                },
                {
                    field: 'specification',
                    title: '规格型号'
                },
                {
                    field: 'unit',
                    title: '单位'
                },
                {
                    field: 'quantity',
                    title: '数量'
                },
                {
                    field: 'cumulativeShipment',
                    title: '累计发货数量'
                },
                {
                    field: 'unshippedQuantity',
                    title: '未发货数量'
                },
                {
                    field: 'currentInventory',
                    title: '减现有：库存'
                },
                {
                    field: 'productionNeeded',
                    title: '需要生产数'
                },
                {
                    field: 'actualInProduction',
                    title: '实际在产数'
                },
                {
                    field: 'difference',
                    title: '差异数'
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
