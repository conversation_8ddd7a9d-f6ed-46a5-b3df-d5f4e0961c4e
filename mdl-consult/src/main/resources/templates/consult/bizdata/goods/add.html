<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增库存商品数')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-goods-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数据分析ID：</label>
                    <div class="col-sm-8">
                        <input name="dataAnalysisId" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">仓库：</label>
                    <div class="col-sm-8">
                        <input name="warehouse" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">物料编码：</label>
                    <div class="col-sm-8">
                        <input name="materialCode" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">物料名称：</label>
                    <div class="col-sm-8">
                        <input name="materialName" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">规格说明：</label>
                    <div class="col-sm-8">
                        <input name="specification" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">上月结存：</label>
                    <div class="col-sm-8">
                        <input name="lastMonthBalance" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">本月入库：</label>
                    <div class="col-sm-8">
                        <input name="currentMonthInbound" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">本月出库：</label>
                    <div class="col-sm-8">
                        <input name="currentMonthOutbound" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">本月结存数：</label>
                    <div class="col-sm-8">
                        <input name="currentMonthBalance" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/bizdata/goods"
        $("#form-goods-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-goods-add').serialize());
            }
        }
    </script>
</body>
</html>