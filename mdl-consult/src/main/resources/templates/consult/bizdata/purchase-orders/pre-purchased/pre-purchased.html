<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('已预购未下采购单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>统计编号：</label>
                                <input type="text" name="statisticsNumber"/>
                            </li>
                            <li>
                                <label>物料编号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>预购编号：</label>
                                <input type="text" name="prePurchaseNo"/>
                            </li>
                            <!--
                            <li>
                                <label>预购日期：</label>
                                <input type="text" class="time-input" placeholder="请选择预购日期" name="prePurchaseDate"/>
                            </li>
                            <li>
                                <label>供应商：</label>
                                <input type="text" name="supplier"/>
                            </li>
                            <li>
                                <label>采购人员：</label>
                                <input type="text" name="purchaser"/>
                            </li>
                            <li>
                                <label>产品名称：</label>
                                <input type="text" name="productName"/>
                            </li>
                            <li>
                                <label>型号：</label>
                                <input type="text" name="model"/>
                            </li>
                            <li>
                                <label>单位：</label>
                                <input type="text" name="unit"/>
                            </li>
                            <li>
                                <label>订单需求数量：</label>
                                <input type="text" name="orderDemandQuantity"/>
                            </li>
                            <li>
                                <label>已采购数量：</label>
                                <input type="text" name="purchasedQuantity"/>
                            </li>
                            <li>
                                <label>剩余采购数量：</label>
                                <input type="text" name="remainingPurchaseQuantity"/>
                            </li>
                            <li>
                                <label>交货日期：</label>
                                <input type="text" class="time-input" placeholder="请选择交货日期" name="deliveryDate"/>
                            </li>
                            <li>
                                <label>客供：</label>
                                <input type="text" name="customerSupplied"/>
                            </li>
                            <li>
                                <label>客户名称：</label>
                                <input type="text" name="customerName"/>
                            </li>
                            <li>
                                <label>预购分类：</label>
                                <input type="text" name="prePurchaseCategory"/>
                            </li>-->
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="consult/bizdata/purchase-orders:pre-purchased:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="consult/bizdata/purchase-orders:pre-purchased:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="consult/bizdata/purchase-orders:pre-purchased:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <!--<a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="consult/bizdata/purchase-orders:pre-purchased:import">
                    <i class="fa fa-upload"></i> 导入
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="consult/bizdata/purchase-orders:pre-purchased:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('consult/bizdata/purchase-orders:pre-purchased:edit')}]];
        var removeFlag = [[${@permission.hasPermi('consult/bizdata/purchase-orders:pre-purchased:remove')}]];
        var prefix = ctx + "consult/bizdata/purchase-orders/pre-purchased";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "已预购未下采购单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'statisticsNumber',
                    title: '统计编号'
                },
                {
                    field: 'prePurchaseDate',
                    title: '预购日期'
                },
                {
                    field: 'prePurchaseNo',
                    title: '预购编号'
                },
                {
                    field: 'supplier',
                    title: '供应商'
                },
                {
                    field: 'purchaser',
                    title: '采购人员'
                },
                {
                    field: 'materialCode',
                    title: '物料编号'
                },
                {
                    field: 'productName',
                    title: '产品名称'
                },
                {
                    field: 'model',
                    title: '型号'
                },
                {
                    field: 'unit',
                    title: '单位'
                },
                {
                    field: 'orderDemandQuantity',
                    title: '订单需求数量'
                },
                {
                    field: 'purchasedQuantity',
                    title: '已采购数量'
                },
                {
                    field: 'remainingPurchaseQuantity',
                    title: '剩余采购数量'
                },
                {
                    field: 'purchaseStatus',
                    title: '采购状态'
                },
                {
                    field: 'deliveryDate',
                    title: '交货日期'
                },
                {
                    field: 'remarks',
                    title: '备注'
                },
                {
                    field: 'customerSupplied',
                    title: '客供'
                },
                {
                    field: 'customerName',
                    title: '客户名称'
                },
                {
                    field: 'prePurchaseCategory',
                    title: '预购分类'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果数据已经存在，更新这条数据。"> 是否更新已经存在的数据
             &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>