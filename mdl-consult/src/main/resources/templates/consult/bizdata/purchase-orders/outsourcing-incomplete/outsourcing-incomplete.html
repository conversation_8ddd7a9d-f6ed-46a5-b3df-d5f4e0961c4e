<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('未完成委外订单列列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>统计编号：</label>
                                <input type="text" name="statisticsNumber"/>
                            </li>
                            <li>
                                <label>委外单号：</label>
                                <input type="text" name="outsourcingOrderNo"/>
                            </li>
                            <li>
                                <label>物料编码：</label>
                                <input type="text" name="materialLongCode"/>
                            </li>
                            <!--
                            <li>
                                <label>供应商：</label>
                                <input type="text" name="supplier"/>
                            </li>
                            <li>
                                <label>订单日期：</label>
                                <input type="text" class="time-input" placeholder="请选择订单日期" name="orderDate"/>
                            </li>
                            <li>
                                <label>交货期：</label>
                                <input type="text" class="time-input" placeholder="请选择交货期" name="deliveryDate"/>
                            </li>
                            <li>
                                <label>物料名称：</label>
                                <input type="text" name="materialName"/>
                            </li>
                            <li>
                                <label>单位：</label>
                                <input type="text" name="unit"/>
                            </li>
                            <li>
                                <label>规格型号：</label>
                                <input type="text" name="specification"/>
                            </li>
                            <li>
                                <label>发出数量：</label>
                                <input type="text" name="sentQuantity"/>
                            </li>
                            <li>
                                <label>累计入库数：</label>
                                <input type="text" name="cumulativeStorage"/>
                            </li>
                            <li>
                                <label>未完成入库数：</label>
                                <input type="text" name="incompleteStorage"/>
                            </li>-->
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="consult/bizdata/purchase-orders:outsourcing-incomplete:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="consult/bizdata/purchase-orders:outsourcing-incomplete:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="consult/bizdata/purchase-orders:outsourcing-incomplete:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <!--<a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="consult/bizdata/purchase-orders:outsourcing-incomplete:import">
                    <i class="fa fa-upload"></i> 导入
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="consult/bizdata/purchase-orders:outsourcing-incomplete:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('consult/bizdata/purchase-orders:outsourcing-incomplete:edit')}]];
        var removeFlag = [[${@permission.hasPermi('consult/bizdata/purchase-orders:outsourcing-incomplete:remove')}]];
        var prefix = ctx + "consult/bizdata/purchase-orders/outsourcing-incomplete";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "未完成委外订单列",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'statisticsNumber',
                    title: '统计编号'
                },
                {
                    field: 'supplier',
                    title: '供应商'
                },
                {
                    field: 'outsourcingOrderNo',
                    title: '委外单号'
                },
                {
                    field: 'orderDate',
                    title: '订单日期'
                },
                {
                    field: 'deliveryDate',
                    title: '交货期'
                },
                {
                    field: 'materialLongCode',
                    title: '物料编码'
                },
                {
                    field: 'materialName',
                    title: '物料名称'
                },
                {
                    field: 'unit',
                    title: '单位'
                },
                {
                    field: 'specification',
                    title: '规格型号'
                },
                {
                    field: 'sentQuantity',
                    title: '发出数量'
                },
                {
                    field: 'cumulativeStorage',
                    title: '累计入库数'
                },
                {
                    field: 'incompleteStorage',
                    title: '未完成入库数'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果数据已经存在，更新这条数据。"> 是否更新已经存在的数据
             &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>