<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.analysis.mapper.ImportTaskMapper">
    
    <resultMap type="ImportTask" id="ImportTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="importType"    column="import_type"    />
        <result property="status"    column="status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="successCount"    column="success_count"    />
        <result property="failureCount"    column="failure_count"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="resultMessage"    column="result_message"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectImportTaskVo">
        select task_id, data_analysis_id, statistics_number, import_type, status, start_time, end_time, success_count, failure_count, error_message, result_message, create_by, create_time, update_by, update_time, remark from mdl_import_task
    </sql>

    <select id="selectImportTaskList" parameterType="ImportTask" resultMap="ImportTaskResult">
        <include refid="selectImportTaskVo"/>
        <where>  
            <if test="dataAnalysisId != null "> and data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null  and statisticsNumber != ''"> and statistics_number like concat('%', #{statisticsNumber}, '%')</if>
            <if test="importType != null  and importType != ''"> and import_type = #{importType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectImportTaskByTaskId" parameterType="Long" resultMap="ImportTaskResult">
        <include refid="selectImportTaskVo"/>
        where task_id = #{taskId}
    </select>
    
    <select id="selectLatestImportTask" resultMap="ImportTaskResult">
        <include refid="selectImportTaskVo"/>
        where data_analysis_id = #{dataAnalysisId} and import_type = #{importType}
        order by create_time desc
        limit 1
    </select>
        
    <insert id="insertImportTask" parameterType="ImportTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into mdl_import_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="statisticsNumber != null">statistics_number,</if>
            <if test="importType != null">import_type,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="successCount != null">success_count,</if>
            <if test="failureCount != null">failure_count,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="resultMessage != null">result_message,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="statisticsNumber != null">#{statisticsNumber},</if>
            <if test="importType != null">#{importType},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="successCount != null">#{successCount},</if>
            <if test="failureCount != null">#{failureCount},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="resultMessage != null">#{resultMessage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateImportTask" parameterType="ImportTask">
        update mdl_import_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="statisticsNumber != null">statistics_number = #{statisticsNumber},</if>
            <if test="importType != null">import_type = #{importType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="successCount != null">success_count = #{successCount},</if>
            <if test="failureCount != null">failure_count = #{failureCount},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="resultMessage != null">result_message = #{resultMessage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteImportTaskByTaskId" parameterType="Long">
        delete from mdl_import_task where task_id = #{taskId}
    </delete>

    <delete id="deleteImportTaskByTaskIds" parameterType="String">
        delete from mdl_import_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
