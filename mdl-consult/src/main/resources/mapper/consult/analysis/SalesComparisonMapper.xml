<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.analysis.mapper.SalesComparisonMapper">
    <!-- 查询销售对比数据列表 -->
    <select id="selectSalesComparisonList" resultType="com.mdl.consult.analysis.domain.SalesOrdersProductionCheck">
        select
            -- 订单日期
            o.order_date orderDate,
            -- 订单号
            o.sales_order_no orderNo,
            -- 生产订单号
            o.production_order_no productionOrderNo,
            -- 客户编码
            -- 客户名称
            o.customer_name customerName,
            -- 存货编码
            o.inventory_code inventoryCode,
            -- 存货名称
            o.inventory_name inventoryName,
            -- 规格型号
            o.specification specification,
            -- 单位
            o.unit,
            -- 数量
            o.quantity,
            -- 累计发货数量
            o.cumulative_shipment cumulativeShipment,
            -- 未发货数量
            o.unshipped_quantity unshippedQuantity,
            -- 减现有：库存
            o.current_balance currentInventory,
            -- 需要生产数
            COALESCE(o.unshipped_quantity,0) - COALESCE(o.current_balance,0) productionNeeded,
            -- 实际在产数
            t1.unstored_quantity actualInProduction,
            -- 差异数
            COALESCE(o.unshipped_quantity,0) - COALESCE(o.current_balance,0) - COALESCE(t1.unstored_quantity,0) difference

        from mdl_sales_orders_incomplete o
        -- 实际在产数
        left join (
            select sales_order_no,material_code,sum(COALESCE(unstored_quantity, 0)) unstored_quantity
            from mdl_production_orders_incomplete where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by sales_order_no,material_code
        ) t1 on t1.material_code = o.inventory_code and t1.sales_order_no = o.sales_order_no

        <where>
            o.data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            <if test="orderNo != null and orderNo != ''">
                and o.sales_order_no = #{orderNo}
            </if>
            <if test="inventoryCode != null and inventoryCode != ''">
                and o.inventory_code = #{inventoryCode}
            </if>
        </where>
    </select>

</mapper>
