<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.analysis.mapper.DataAnalysisMapper">

    <resultMap type="DataAnalysis" id="DataAnalysisResult">
        <result property="id"    column="id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDataAnalysisVo">
        select id, statistics_number, create_by, create_time, update_by, update_time, remark from mdl_data_analysis
    </sql>

    <select id="selectDataAnalysisList" parameterType="DataAnalysis" resultMap="DataAnalysisResult">
        <include refid="selectDataAnalysisVo"/>
        <where>
            <if test="statisticsNumber != null  and statisticsNumber != ''"> and statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectDataAnalysisById" parameterType="Long" resultMap="DataAnalysisResult">
        <include refid="selectDataAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataAnalysis" parameterType="DataAnalysis" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_data_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statisticsNumber != null">statistics_number,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statisticsNumber != null">#{statisticsNumber},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDataAnalysis" parameterType="DataAnalysis">
        update mdl_data_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="statisticsNumber != null">statistics_number = #{statisticsNumber},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataAnalysisById" parameterType="Long">
        delete from mdl_data_analysis where id = #{id}
    </delete>

    <delete id="deleteDataAnalysisByIds" parameterType="String">
        delete from mdl_data_analysis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkStatisticsNumberUnique" parameterType="String" resultMap="DataAnalysisResult">
        <include refid="selectDataAnalysisVo"/>
        where statistics_number = #{statisticsNumber} limit 1
    </select>

</mapper>