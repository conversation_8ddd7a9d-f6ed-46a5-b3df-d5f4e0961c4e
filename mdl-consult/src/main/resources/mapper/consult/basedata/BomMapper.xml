<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.basedata.mapper.BomMapper">

    <resultMap type="Bom" id="BomResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="bomLevel"    column="bom_level"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="specification"    column="specification"    />
        <result property="childQuantity"    column="child_quantity"    />
        <result property="measurementUnitStandard"    column="measurement_unit_standard"    />
        <result property="mainUnitStandard"    column="main_unit_standard"    />
        <result property="totalParentQuantity"    column="total_parent_quantity"    />
        <result property="totalChildQuantity"    column="total_child_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBomVo">
        select b.id, b.data_analysis_id,d.statistics_number, b.parent_code, b.bom_level, b.material_code, b.material_name, b.specification, b.child_quantity, b.measurement_unit_standard, b.main_unit_standard, b.total_parent_quantity, b.total_child_quantity, b.create_by, b.create_time, b.update_by, b.update_time, b.remark
        from mdl_bom b
        left join mdl_data_analysis d on b.data_analysis_id = d.id
    </sql>

    <select id="selectBomList" parameterType="Bom" resultMap="BomResult">
        <include refid="selectBomVo"/>
        <where>
            <if test="dataAnalysisId != null "> and b.data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
            <if test="parentCode != null  and parentCode != ''"> and b.parent_code = #{parentCode}</if>
            <if test="bomLevel != null "> and b.bom_level = #{bomLevel}</if>
            <if test="materialCode != null  and materialCode != ''"> and b.material_code = #{materialCode}</if>
            <if test="materialName != null  and materialName != ''"> and b.material_name like concat('%', #{materialName}, '%')</if>
            <if test="specification != null  and specification != ''"> and b.specification = #{specification}</if>
            <if test="childQuantity != null "> and b.child_quantity = #{childQuantity}</if>
            <if test="measurementUnitStandard != null  and measurementUnitStandard != ''"> and b.measurement_unit_standard = #{measurementUnitStandard}</if>
            <if test="mainUnitStandard != null "> and b.main_unit_standard = #{mainUnitStandard}</if>
            <if test="totalParentQuantity != null "> and b.total_parent_quantity = #{totalParentQuantity}</if>
            <if test="totalChildQuantity != null "> and b.total_child_quantity = #{totalChildQuantity}</if>
        </where>
    </select>

    <select id="selectBomById" parameterType="Long" resultMap="BomResult">
        <include refid="selectBomVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertBom" parameterType="Bom" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_bom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="bomLevel != null">bom_level,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="specification != null">specification,</if>
            <if test="childQuantity != null">child_quantity,</if>
            <if test="measurementUnitStandard != null">measurement_unit_standard,</if>
            <if test="mainUnitStandard != null">main_unit_standard,</if>
            <if test="totalParentQuantity != null">total_parent_quantity,</if>
            <if test="totalChildQuantity != null">total_child_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="bomLevel != null">#{bomLevel},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="childQuantity != null">#{childQuantity},</if>
            <if test="measurementUnitStandard != null">#{measurementUnitStandard},</if>
            <if test="mainUnitStandard != null">#{mainUnitStandard},</if>
            <if test="totalParentQuantity != null">#{totalParentQuantity},</if>
            <if test="totalChildQuantity != null">#{totalChildQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBom" parameterType="Bom">
        update mdl_bom
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="bomLevel != null">bom_level = #{bomLevel},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="childQuantity != null">child_quantity = #{childQuantity},</if>
            <if test="measurementUnitStandard != null">measurement_unit_standard = #{measurementUnitStandard},</if>
            <if test="mainUnitStandard != null">main_unit_standard = #{mainUnitStandard},</if>
            <if test="totalParentQuantity != null">total_parent_quantity = #{totalParentQuantity},</if>
            <if test="totalChildQuantity != null">total_child_quantity = #{totalChildQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBomById" parameterType="Long">
        delete from mdl_bom where id = #{id}
    </delete>

    <delete id="deleteBomByIds" parameterType="String">
        delete from mdl_bom where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBomByDataAnalysisId" parameterType="Long">
        delete from mdl_bom where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>