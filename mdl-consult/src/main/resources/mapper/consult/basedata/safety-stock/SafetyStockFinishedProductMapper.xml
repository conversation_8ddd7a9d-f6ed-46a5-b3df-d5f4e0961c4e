<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.basedata.mapper.SafetyStockFinishedProductMapper">

    <resultMap type="SafetyStockFinishedProduct" id="SafetyStockFinishedProductResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="inventoryCode"    column="inventory_code"    />
        <result property="inventoryName"    column="inventory_name"    />
        <result property="specification"    column="specification"    />
        <result property="unit"    column="unit"    />
        <result property="safetyStock"    column="safety_stock"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSafetyStockFinishedProductVo">
        select b.id, b.data_analysis_id,d.statistics_number, b.inventory_code, b.inventory_name, b.specification, b.unit, b.safety_stock, b.create_by, b.create_time, b.update_by, b.update_time, b.remark
        from mdl_safety_stock_finished_product b
        left join mdl_data_analysis d on b.data_analysis_id = d.id
    </sql>

    <select id="selectSafetyStockFinishedProductList" parameterType="SafetyStockFinishedProduct" resultMap="SafetyStockFinishedProductResult">
        <include refid="selectSafetyStockFinishedProductVo"/>
        <where>
            <if test="dataAnalysisId != null "> and b.data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
            <if test="inventoryCode != null  and inventoryCode != ''"> and b.inventory_code = #{inventoryCode}</if>
            <if test="inventoryName != null  and inventoryName != ''"> and b.inventory_name like concat('%', #{inventoryName}, '%')</if>
            <if test="specification != null  and specification != ''"> and b.specification = #{specification}</if>
            <if test="unit != null  and unit != ''"> and b.unit = #{unit}</if>
            <if test="safetyStock != null "> and b.safety_stock = #{safetyStock}</if>
        </where>
    </select>

    <select id="selectSafetyStockFinishedProductById" parameterType="Long" resultMap="SafetyStockFinishedProductResult">
        <include refid="selectSafetyStockFinishedProductVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertSafetyStockFinishedProduct" parameterType="SafetyStockFinishedProduct" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_safety_stock_finished_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="statisticsNumber != null">data_analysis_id,</if>
            <if test="inventoryCode != null">inventory_code,</if>
            <if test="inventoryName != null">inventory_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unit != null">unit,</if>
            <if test="safetyStock != null">safety_stock,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="statisticsNumber != null">(select id from mdl_data_analysis where statistics_number = #{statisticsNumber}),</if>
            <if test="inventoryCode != null">#{inventoryCode},</if>
            <if test="inventoryName != null">#{inventoryName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unit != null">#{unit},</if>
            <if test="safetyStock != null">#{safetyStock},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSafetyStockFinishedProduct" parameterType="SafetyStockFinishedProduct">
        update mdl_safety_stock_finished_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="statisticsNumber != null">data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber}),</if>
            <if test="inventoryCode != null">inventory_code = #{inventoryCode},</if>
            <if test="inventoryName != null">inventory_name = #{inventoryName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="safetyStock != null">safety_stock = #{safetyStock},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSafetyStockFinishedProductById" parameterType="Long">
        delete from mdl_safety_stock_finished_product where id = #{id}
    </delete>

    <delete id="deleteSafetyStockFinishedProductByIds" parameterType="String">
        delete from mdl_safety_stock_finished_product where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSafetyStockFinishedProductByDataAnalysisId" parameterType="Long">
        delete from mdl_safety_stock_finished_product where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>