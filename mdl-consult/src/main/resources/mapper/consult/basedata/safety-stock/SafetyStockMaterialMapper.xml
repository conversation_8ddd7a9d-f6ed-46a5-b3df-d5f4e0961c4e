<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.basedata.mapper.SafetyStockMaterialMapper">

    <resultMap type="SafetyStockMaterial" id="SafetyStockMaterialResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="warehouse"    column="warehouse"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="specification"    column="specification"    />
        <result property="safetyStock"    column="safety_stock"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSafetyStockMaterialVo">
        select b.id,b.data_analysis_id, d.statistics_number, b.warehouse, b.material_code, b.material_name, b.specification, b.safety_stock, b.create_by, b.create_time, b.update_by, b.update_time, b.remark
        from mdl_safety_stock_material b
        left join mdl_data_analysis d on b.data_analysis_id = d.id
    </sql>

    <select id="selectSafetyStockMaterialList" parameterType="SafetyStockMaterial" resultMap="SafetyStockMaterialResult">
        <include refid="selectSafetyStockMaterialVo"/>
        <where>
            <if test="dataAnalysisId != null "> and b.data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
            <if test="warehouse != null  and warehouse != ''"> and b.warehouse = #{warehouse}</if>
            <if test="materialCode != null  and materialCode != ''"> and b.material_code = #{materialCode}</if>
            <if test="materialName != null  and materialName != ''"> and b.material_name like concat('%', #{materialName}, '%')</if>
            <if test="specification != null  and specification != ''"> and b.specification = #{specification}</if>
            <if test="safetyStock != null "> and b.safety_stock = #{safetyStock}</if>
        </where>
    </select>

    <select id="selectSafetyStockMaterialById" parameterType="Long" resultMap="SafetyStockMaterialResult">
        <include refid="selectSafetyStockMaterialVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertSafetyStockMaterial" parameterType="SafetyStockMaterial" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_safety_stock_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="statisticsNumber != null">data_analysis_id,</if>
            <if test="warehouse != null">warehouse,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="specification != null">specification,</if>
            <if test="safetyStock != null">safety_stock,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="statisticsNumber != null">(select id from mdl_data_analysis where statistics_number = #{statisticsNumber}),</if>
            <if test="warehouse != null">#{warehouse},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="safetyStock != null">#{safetyStock},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSafetyStockMaterial" parameterType="SafetyStockMaterial">
        update mdl_safety_stock_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="statisticsNumber != null">data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber}),</if>
            <if test="warehouse != null">warehouse = #{warehouse},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="safetyStock != null">safety_stock = #{safetyStock},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSafetyStockMaterialById" parameterType="Long">
        delete from mdl_safety_stock_material where id = #{id}
    </delete>

    <delete id="deleteSafetyStockMaterialByIds" parameterType="String">
        delete from mdl_safety_stock_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSafetyStockMaterialByDataAnalysisId" parameterType="Long">
        delete from mdl_safety_stock_material where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>