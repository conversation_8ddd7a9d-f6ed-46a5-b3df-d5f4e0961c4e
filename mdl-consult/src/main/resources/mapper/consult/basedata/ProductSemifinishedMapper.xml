<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.basedata.mapper.ProductSemifinishedMapper">

    <resultMap type="ProductSemifinished" id="ProductSemifinishedResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="neutralCode"    column="neutral_code"    />
        <result property="materialCode"    column="material_code"    />
        <result property="quantity"    column="quantity"    />
        <result property="workshopInventory"    column="workshop_inventory"    />
        <result property="productionRequired"    column="production_required"    />
        <result property="isMaster"    column="is_master"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProductSemifinishedVo">
        select b.id,b.data_analysis_id, d.statistics_number, b.neutral_code,b.material_code, b.quantity, b.workshop_inventory, b.production_required,b.is_master, b.create_by, b.create_time, b.update_by, b.update_time, b.remark
        from mdl_product_semifinished b
        left join mdl_data_analysis d on b.data_analysis_id = d.id
    </sql>

    <select id="selectProductSemifinishedList" parameterType="ProductSemifinished" resultMap="ProductSemifinishedResult">
        <include refid="selectProductSemifinishedVo"/>
        <where>
            <if test="dataAnalysisId != null "> and b.data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
            <if test="neutralCode != null  and neutralCode != ''"> and b.neutral_code = #{neutralCode}</if>
            <if test="materialCode != null  and materialCode != ''"> and b.material_code = #{materialCode}</if>
            <if test="quantity != null "> and b.quantity = #{quantity}</if>
            <if test="workshopInventory != null "> and b.workshop_inventory = #{workshopInventory}</if>
            <if test="productionRequired != null "> and b.production_required = #{productionRequired}</if>
        </where>
    </select>

    <select id="selectProductSemifinishedById" parameterType="Long" resultMap="ProductSemifinishedResult">
        <include refid="selectProductSemifinishedVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertProductSemifinished" parameterType="ProductSemifinished" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_product_semifinished
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="statisticsNumber != null">data_analysis_id,</if>
            <if test="neutralCode != null">neutral_code,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="quantity != null">quantity,</if>
            <if test="workshopInventory != null">workshop_inventory,</if>
            <if test="productionRequired != null">production_required,</if>
            <if test="isMaster != null">is_master,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null"> #{dataAnalysisId},</if>
            <if test="statisticsNumber != null">(select id from mdl_data_analysis where statistics_number = #{statisticsNumber}),</if>
            <if test="neutralCode != null">#{neutralCode},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="workshopInventory != null">#{workshopInventory},</if>
            <if test="productionRequired != null">#{productionRequired},</if>
            <if test="isMaster != null">#{isMaster},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProductSemifinished" parameterType="ProductSemifinished">
        update mdl_product_semifinished
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="statisticsNumber != null">data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber}),</if>
            <if test="neutralCode != null">neutral_code = #{neutralCode},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="workshopInventory != null">workshop_inventory = #{workshopInventory},</if>
            <if test="productionRequired != null">production_required = #{productionRequired},</if>
            <if test="isMaster != null">is_master = #{isMaster},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductSemifinishedById" parameterType="Long">
        delete from mdl_product_semifinished where id = #{id}
    </delete>

    <delete id="deleteProductSemifinishedByIds" parameterType="String">
        delete from mdl_product_semifinished where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProductSemifinishedByDataAnalysisId" parameterType="Long">
        delete from mdl_product_semifinished where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>