<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.basedata.mapper.BomPackagingMapper">

    <resultMap type="BomPackaging" id="BomPackagingResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber"    column="statistics_number"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="parentName"    column="parent_name"    />
        <result property="childCode"    column="child_code"    />
        <result property="childName"    column="child_name"    />
        <result property="specification"    column="specification"    />
        <result property="childQuantity"    column="child_quantity"    />
        <result property="childUnit"    column="child_unit"    />
        <result property="orderQuantity"    column="order_quantity"    />
        <result property="materialRequirementSubtotal"    column="material_requirement_subtotal"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBomPackagingVo">
        select b.id,b.data_analysis_id, d.statistics_number, b.parent_code, b.parent_name, b.child_code, b.child_name, b.specification, b.child_quantity, b.child_unit, b.order_quantity, b.material_requirement_subtotal, b.create_by, b.create_time, b.update_by, b.update_time, b.remark
        from mdl_bom_packaging b
        left join mdl_data_analysis d on b.data_analysis_id = d.id
    </sql>

    <select id="selectBomPackagingList" parameterType="BomPackaging" resultMap="BomPackagingResult">
        <include refid="selectBomPackagingVo"/>
        <where>
            <if test="dataAnalysisId != null "> and b.data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
            <if test="parentCode != null  and parentCode != ''"> and b.parent_code = #{parentCode}</if>
            <if test="parentName != null  and parentName != ''"> and b.parent_name like concat('%', #{parentName}, '%')</if>
            <if test="childCode != null  and childCode != ''"> and b.child_code = #{childCode}</if>
            <if test="childName != null  and childName != ''"> and b.child_name like concat('%', #{childName}, '%')</if>
            <if test="specification != null  and specification != ''"> and b.specification = #{specification}</if>
            <if test="childQuantity != null "> and b.child_quantity = #{childQuantity}</if>
            <if test="childUnit != null  and childUnit != ''"> and b.child_unit = #{childUnit}</if>
            <if test="orderQuantity != null "> and b.order_quantity = #{orderQuantity}</if>
            <if test="materialRequirementSubtotal != null "> and b.material_requirement_subtotal = #{materialRequirementSubtotal}</if>
        </where>
    </select>

    <select id="selectBomPackagingById" parameterType="Long" resultMap="BomPackagingResult">
        <include refid="selectBomPackagingVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertBomPackaging" parameterType="BomPackaging" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_bom_packaging
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="parentName != null">parent_name,</if>
            <if test="childCode != null">child_code,</if>
            <if test="childName != null">child_name,</if>
            <if test="specification != null">specification,</if>
            <if test="childQuantity != null">child_quantity,</if>
            <if test="childUnit != null">child_unit,</if>
            <if test="orderQuantity != null">order_quantity,</if>
            <if test="materialRequirementSubtotal != null">material_requirement_subtotal,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="parentName != null">#{parentName},</if>
            <if test="childCode != null">#{childCode},</if>
            <if test="childName != null">#{childName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="childQuantity != null">#{childQuantity},</if>
            <if test="childUnit != null">#{childUnit},</if>
            <if test="orderQuantity != null">#{orderQuantity},</if>
            <if test="materialRequirementSubtotal != null">#{materialRequirementSubtotal},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBomPackaging" parameterType="BomPackaging">
        update mdl_bom_packaging
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="parentName != null">parent_name = #{parentName},</if>
            <if test="childCode != null">child_code = #{childCode},</if>
            <if test="childName != null">child_name = #{childName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="childQuantity != null">child_quantity = #{childQuantity},</if>
            <if test="childUnit != null">child_unit = #{childUnit},</if>
            <if test="orderQuantity != null">order_quantity = #{orderQuantity},</if>
            <if test="materialRequirementSubtotal != null">material_requirement_subtotal = #{materialRequirementSubtotal},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBomPackagingById" parameterType="Long">
        delete from mdl_bom_packaging where id = #{id}
    </delete>

    <delete id="deleteBomPackagingByIds" parameterType="String">
        delete from mdl_bom_packaging where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBomPackagingByDataAnalysisId" parameterType="Long">
        delete from mdl_bom_packaging where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>