<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.InventoryGoodsMapper">

    <resultMap type="InventoryGoods" id="InventoryGoodsResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="warehouseType"    column="warehouse_type"    />
        <result property="warehouse"    column="warehouse"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="specification"    column="specification"    />
        <result property="lastMonthBalance"    column="last_month_balance"    />
        <result property="currentMonthInbound"    column="current_month_inbound"    />
        <result property="currentMonthOutbound"    column="current_month_outbound"    />
        <result property="currentMonthBalance"    column="current_month_balance"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectInventoryGoodsVo">
        select a.id, a.data_analysis_id, a.warehouse_type,a.warehouse,a.material_code, a.material_name, a.specification, a.last_month_balance, a.current_month_inbound, a.current_month_outbound, a.current_month_balance, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_inventory_goods a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectInventoryGoodsList" parameterType="InventoryGoods" resultMap="InventoryGoodsResult">
        <include refid="selectInventoryGoodsVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="warehouseType != null  and warehouseType != ''"> and a.warehouse_type = #{warehouseType}</if>
            <if test="warehouse != null  and warehouse != ''"> and a.warehouse = #{warehouse}</if>
            <if test="materialCode != null  and materialCode != ''"> and a.material_code = #{materialCode}</if>
            <if test="materialName != null  and materialName != ''"> and a.material_name like concat('%', #{materialName}, '%')</if>
            <if test="specification != null  and specification != ''"> and a.specification = #{specification}</if>
            <if test="lastMonthBalance != null "> and a.last_month_balance = #{lastMonthBalance}</if>
            <if test="currentMonthInbound != null "> and a.current_month_inbound = #{currentMonthInbound}</if>
            <if test="currentMonthOutbound != null "> and a.current_month_outbound = #{currentMonthOutbound}</if>
            <if test="currentMonthBalance != null "> and a.current_month_balance = #{currentMonthBalance}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectInventoryGoodsById" parameterType="Long" resultMap="InventoryGoodsResult">
        <include refid="selectInventoryGoodsVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertInventoryGoods" parameterType="InventoryGoods" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_inventory_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="warehouseType != null">warehouse_type,</if>
            <if test="warehouse != null">warehouse,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="specification != null">specification,</if>
            <if test="lastMonthBalance != null">last_month_balance,</if>
            <if test="currentMonthInbound != null">current_month_inbound,</if>
            <if test="currentMonthOutbound != null">current_month_outbound,</if>
            <if test="currentMonthBalance != null">current_month_balance,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="warehouseType != null">#{warehouseType},</if>
            <if test="warehouse != null">#{warehouse},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="lastMonthBalance != null">#{lastMonthBalance},</if>
            <if test="currentMonthInbound != null">#{currentMonthInbound},</if>
            <if test="currentMonthOutbound != null">#{currentMonthOutbound},</if>
            <if test="currentMonthBalance != null">#{currentMonthBalance},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateInventoryGoods" parameterType="InventoryGoods">
        update mdl_inventory_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="warehouseType != null">warehouse_type = #{warehouseType},</if>
            <if test="warehouse != null">warehouse = #{warehouse},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="lastMonthBalance != null">last_month_balance = #{lastMonthBalance},</if>
            <if test="currentMonthInbound != null">current_month_inbound = #{currentMonthInbound},</if>
            <if test="currentMonthOutbound != null">current_month_outbound = #{currentMonthOutbound},</if>
            <if test="currentMonthBalance != null">current_month_balance = #{currentMonthBalance},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryGoodsById" parameterType="Long">
        delete from mdl_inventory_goods where id = #{id}
    </delete>

    <delete id="deleteInventoryGoodsByIds" parameterType="String">
        delete from mdl_inventory_goods where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteInventoryGoodsByDataAnalysisId" parameterType="Long">
        delete from mdl_inventory_goods where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>