<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.PurchaseOrdersIncompleteMapper">

    <resultMap type="PurchaseOrdersIncomplete" id="PurchaseOrdersIncompleteResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="documentDate"    column="document_date"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="purchaseOrderNo"    column="purchase_order_no"    />
        <result property="transactionType"    column="transaction_type"    />
        <result property="supplier"    column="supplier"    />
        <result property="documentStatus"    column="document_status"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="specification"    column="specification"    />
        <result property="quantity"    column="quantity"    />
        <result property="cumulativeStorage"    column="cumulative_storage"    />
        <result property="unstoredQuantity"    column="unstored_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectPurchaseOrdersIncompleteVo">
        select a.id, a.data_analysis_id, a.document_date, a.delivery_date, a.purchase_order_no, a.transaction_type, a.supplier, a.document_status, a.material_code, a.material_name, a.specification, a.quantity, a.cumulative_storage, a.unstored_quantity, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_purchase_orders_incomplete a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectPurchaseOrdersIncompleteList" parameterType="PurchaseOrdersIncomplete" resultMap="PurchaseOrdersIncompleteResult">
        <include refid="selectPurchaseOrdersIncompleteVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="documentDate != null "> and a.document_date = #{documentDate}</if>
            <if test="deliveryDate != null "> and a.delivery_date = #{deliveryDate}</if>
            <if test="purchaseOrderNo != null  and purchaseOrderNo != ''"> and a.purchase_order_no = #{purchaseOrderNo}</if>
            <if test="transactionType != null  and transactionType != ''"> and a.transaction_type = #{transactionType}</if>
            <if test="supplier != null  and supplier != ''"> and a.supplier = #{supplier}</if>
            <if test="documentStatus != null  and documentStatus != ''"> and a.document_status = #{documentStatus}</if>
            <if test="materialCode != null  and materialCode != ''"> and a.material_code = #{materialCode}</if>
            <if test="materialName != null  and materialName != ''"> and a.material_name like concat('%', #{materialName}, '%')</if>
            <if test="specification != null  and specification != ''"> and a.specification = #{specification}</if>
            <if test="quantity != null "> and a.quantity = #{quantity}</if>
            <if test="cumulativeStorage != null "> and a.cumulative_storage = #{cumulativeStorage}</if>
            <if test="unstoredQuantity != null "> and a.unstored_quantity = #{unstoredQuantity}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectPurchaseOrdersIncompleteById" parameterType="Long" resultMap="PurchaseOrdersIncompleteResult">
        <include refid="selectPurchaseOrdersIncompleteVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertPurchaseOrdersIncomplete" parameterType="PurchaseOrdersIncomplete" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_purchase_orders_incomplete
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="documentDate != null">document_date,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="purchaseOrderNo != null">purchase_order_no,</if>
            <if test="transactionType != null">transaction_type,</if>
            <if test="supplier != null">supplier,</if>
            <if test="documentStatus != null">document_status,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="specification != null">specification,</if>
            <if test="quantity != null">quantity,</if>
            <if test="cumulativeStorage != null">cumulative_storage,</if>
            <if test="unstoredQuantity != null">unstored_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="documentDate != null">#{documentDate},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="purchaseOrderNo != null">#{purchaseOrderNo},</if>
            <if test="transactionType != null">#{transactionType},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="documentStatus != null">#{documentStatus},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="cumulativeStorage != null">#{cumulativeStorage},</if>
            <if test="unstoredQuantity != null">#{unstoredQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePurchaseOrdersIncomplete" parameterType="PurchaseOrdersIncomplete">
        update mdl_purchase_orders_incomplete
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="documentDate != null">document_date = #{documentDate},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="purchaseOrderNo != null">purchase_order_no = #{purchaseOrderNo},</if>
            <if test="transactionType != null">transaction_type = #{transactionType},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="documentStatus != null">document_status = #{documentStatus},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="cumulativeStorage != null">cumulative_storage = #{cumulativeStorage},</if>
            <if test="unstoredQuantity != null">unstored_quantity = #{unstoredQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePurchaseOrdersIncompleteById" parameterType="Long">
        delete from mdl_purchase_orders_incomplete where id = #{id}
    </delete>

    <delete id="deletePurchaseOrdersIncompleteByIds" parameterType="String">
        delete from mdl_purchase_orders_incomplete where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePurchaseOrdersIncompleteByDataAnalysisId" parameterType="Long">
        delete from mdl_purchase_orders_incomplete where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>