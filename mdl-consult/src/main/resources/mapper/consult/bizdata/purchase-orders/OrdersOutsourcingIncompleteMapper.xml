<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.OrdersOutsourcingIncompleteMapper">

    <resultMap type="OrdersOutsourcingIncomplete" id="OrdersOutsourcingIncompleteResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="supplier"    column="supplier"    />
        <result property="outsourcingOrderNo"    column="outsourcing_order_no"    />
        <result property="orderDate"    column="order_date"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="materialLongCode"    column="material_long_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="unit"    column="unit"    />
        <result property="specification"    column="specification"    />
        <result property="sentQuantity"    column="sent_quantity"    />
        <result property="cumulativeStorage"    column="cumulative_storage"    />
        <result property="incompleteStorage"    column="incomplete_storage"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectOrdersOutsourcingIncompleteVo">
        select a.id, a.data_analysis_id, a.supplier, a.outsourcing_order_no, a.order_date, a.delivery_date, a.material_long_code, a.material_name, a.unit, a.specification, a.sent_quantity, a.cumulative_storage, a.incomplete_storage, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_orders_outsourcing_incomplete a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectOrdersOutsourcingIncompleteList" parameterType="OrdersOutsourcingIncomplete" resultMap="OrdersOutsourcingIncompleteResult">
        <include refid="selectOrdersOutsourcingIncompleteVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="supplier != null  and supplier != ''"> and a.supplier = #{supplier}</if>
            <if test="outsourcingOrderNo != null  and outsourcingOrderNo != ''"> and a.outsourcing_order_no = #{outsourcingOrderNo}</if>
            <if test="orderDate != null "> and a.order_date = #{orderDate}</if>
            <if test="deliveryDate != null "> and a.delivery_date = #{deliveryDate}</if>
            <if test="materialLongCode != null  and materialLongCode != ''"> and a.material_long_code = #{materialLongCode}</if>
            <if test="materialName != null  and materialName != ''"> and a.material_name like concat('%', #{materialName}, '%')</if>
            <if test="unit != null  and unit != ''"> and a.unit = #{unit}</if>
            <if test="specification != null  and specification != ''"> and a.specification = #{specification}</if>
            <if test="sentQuantity != null "> and a.sent_quantity = #{sentQuantity}</if>
            <if test="cumulativeStorage != null "> and a.cumulative_storage = #{cumulativeStorage}</if>
            <if test="incompleteStorage != null "> and a.incomplete_storage = #{incompleteStorage}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectOrdersOutsourcingIncompleteById" parameterType="Long" resultMap="OrdersOutsourcingIncompleteResult">
        <include refid="selectOrdersOutsourcingIncompleteVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertOrdersOutsourcingIncomplete" parameterType="OrdersOutsourcingIncomplete" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_orders_outsourcing_incomplete
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="supplier != null">supplier,</if>
            <if test="outsourcingOrderNo != null">outsourcing_order_no,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="materialLongCode != null">material_long_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="unit != null">unit,</if>
            <if test="specification != null">specification,</if>
            <if test="sentQuantity != null">sent_quantity,</if>
            <if test="cumulativeStorage != null">cumulative_storage,</if>
            <if test="incompleteStorage != null">incomplete_storage,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="outsourcingOrderNo != null">#{outsourcingOrderNo},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="materialLongCode != null">#{materialLongCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="unit != null">#{unit},</if>
            <if test="specification != null">#{specification},</if>
            <if test="sentQuantity != null">#{sentQuantity},</if>
            <if test="cumulativeStorage != null">#{cumulativeStorage},</if>
            <if test="incompleteStorage != null">#{incompleteStorage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOrdersOutsourcingIncomplete" parameterType="OrdersOutsourcingIncomplete">
        update mdl_orders_outsourcing_incomplete
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="outsourcingOrderNo != null">outsourcing_order_no = #{outsourcingOrderNo},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="materialLongCode != null">material_long_code = #{materialLongCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="sentQuantity != null">sent_quantity = #{sentQuantity},</if>
            <if test="cumulativeStorage != null">cumulative_storage = #{cumulativeStorage},</if>
            <if test="incompleteStorage != null">incomplete_storage = #{incompleteStorage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrdersOutsourcingIncompleteById" parameterType="Long">
        delete from mdl_orders_outsourcing_incomplete where id = #{id}
    </delete>

    <delete id="deleteOrdersOutsourcingIncompleteByIds" parameterType="String">
        delete from mdl_orders_outsourcing_incomplete where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteOrdersOutsourcingIncompleteByDataAnalysisId" parameterType="Long">
        delete from mdl_orders_outsourcing_incomplete where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>