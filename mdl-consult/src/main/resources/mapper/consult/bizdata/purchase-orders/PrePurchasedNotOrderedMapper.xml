<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.PrePurchasedNotOrderedMapper">

    <resultMap type="PrePurchasedNotOrdered" id="PrePurchasedNotOrderedResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="prePurchaseDate"    column="pre_purchase_date"    />
        <result property="prePurchaseNo"    column="pre_purchase_no"    />
        <result property="supplier"    column="supplier"    />
        <result property="purchaser"    column="purchaser"    />
        <result property="materialCode"    column="material_code"    />
        <result property="productName"    column="product_name"    />
        <result property="model"    column="model"    />
        <result property="unit"    column="unit"    />
        <result property="orderDemandQuantity"    column="order_demand_quantity"    />
        <result property="purchasedQuantity"    column="purchased_quantity"    />
        <result property="remainingPurchaseQuantity"    column="remaining_purchase_quantity"    />
        <result property="purchaseStatus"    column="purchase_status"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="customerSupplied"    column="customer_supplied"    />
        <result property="customerName"    column="customer_name"    />
        <result property="prePurchaseCategory"    column="pre_purchase_category"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectPrePurchasedNotOrderedVo">
        select a.id, a.data_analysis_id, a.pre_purchase_date, a.pre_purchase_no, a.supplier, a.purchaser, a.material_code, a.product_name, a.model, a.unit, a.order_demand_quantity, a.purchased_quantity, a.remaining_purchase_quantity, a.purchase_status, a.delivery_date, a.remarks, a.customer_supplied, a.customer_name, a.pre_purchase_category, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_pre_purchased_not_ordered a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectPrePurchasedNotOrderedList" parameterType="PrePurchasedNotOrdered" resultMap="PrePurchasedNotOrderedResult">
        <include refid="selectPrePurchasedNotOrderedVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="prePurchaseDate != null "> and a.pre_purchase_date = #{prePurchaseDate}</if>
            <if test="prePurchaseNo != null  and prePurchaseNo != ''"> and a.pre_purchase_no = #{prePurchaseNo}</if>
            <if test="supplier != null  and supplier != ''"> and a.supplier = #{supplier}</if>
            <if test="purchaser != null  and purchaser != ''"> and a.purchaser = #{purchaser}</if>
            <if test="materialCode != null  and materialCode != ''"> and a.material_code = #{materialCode}</if>
            <if test="productName != null  and productName != ''"> and a.product_name like concat('%', #{productName}, '%')</if>
            <if test="model != null  and model != ''"> and a.model = #{model}</if>
            <if test="unit != null  and unit != ''"> and a.unit = #{unit}</if>
            <if test="orderDemandQuantity != null "> and a.order_demand_quantity = #{orderDemandQuantity}</if>
            <if test="purchasedQuantity != null "> and a.purchased_quantity = #{purchasedQuantity}</if>
            <if test="remainingPurchaseQuantity != null "> and a.remaining_purchase_quantity = #{remainingPurchaseQuantity}</if>
            <if test="purchaseStatus != null  and purchaseStatus != ''"> and a.purchase_status = #{purchaseStatus}</if>
            <if test="deliveryDate != null "> and a.delivery_date = #{deliveryDate}</if>
            <if test="remarks != null  and remarks != ''"> and a.remarks = #{remarks}</if>
            <if test="customerSupplied != null "> and a.customer_supplied = #{customerSupplied}</if>
            <if test="customerName != null  and customerName != ''"> and a.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="prePurchaseCategory != null  and prePurchaseCategory != ''"> and a.pre_purchase_category = #{prePurchaseCategory}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectPrePurchasedNotOrderedById" parameterType="Long" resultMap="PrePurchasedNotOrderedResult">
        <include refid="selectPrePurchasedNotOrderedVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertPrePurchasedNotOrdered" parameterType="PrePurchasedNotOrdered" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_pre_purchased_not_ordered
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="prePurchaseDate != null">pre_purchase_date,</if>
            <if test="prePurchaseNo != null">pre_purchase_no,</if>
            <if test="supplier != null">supplier,</if>
            <if test="purchaser != null">purchaser,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="model != null">model,</if>
            <if test="unit != null">unit,</if>
            <if test="orderDemandQuantity != null">order_demand_quantity,</if>
            <if test="purchasedQuantity != null">purchased_quantity,</if>
            <if test="remainingPurchaseQuantity != null">remaining_purchase_quantity,</if>
            <if test="purchaseStatus != null">purchase_status,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="customerSupplied != null">customer_supplied,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="prePurchaseCategory != null">pre_purchase_category,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="prePurchaseDate != null">#{prePurchaseDate},</if>
            <if test="prePurchaseNo != null">#{prePurchaseNo},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="purchaser != null">#{purchaser},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="productName != null">#{productName},</if>
            <if test="model != null">#{model},</if>
            <if test="unit != null">#{unit},</if>
            <if test="orderDemandQuantity != null">#{orderDemandQuantity},</if>
            <if test="purchasedQuantity != null">#{purchasedQuantity},</if>
            <if test="remainingPurchaseQuantity != null">#{remainingPurchaseQuantity},</if>
            <if test="purchaseStatus != null">#{purchaseStatus},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="customerSupplied != null">#{customerSupplied},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="prePurchaseCategory != null">#{prePurchaseCategory},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePrePurchasedNotOrdered" parameterType="PrePurchasedNotOrdered">
        update mdl_pre_purchased_not_ordered
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="prePurchaseDate != null">pre_purchase_date = #{prePurchaseDate},</if>
            <if test="prePurchaseNo != null">pre_purchase_no = #{prePurchaseNo},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="purchaser != null">purchaser = #{purchaser},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="model != null">model = #{model},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="orderDemandQuantity != null">order_demand_quantity = #{orderDemandQuantity},</if>
            <if test="purchasedQuantity != null">purchased_quantity = #{purchasedQuantity},</if>
            <if test="remainingPurchaseQuantity != null">remaining_purchase_quantity = #{remainingPurchaseQuantity},</if>
            <if test="purchaseStatus != null">purchase_status = #{purchaseStatus},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="customerSupplied != null">customer_supplied = #{customerSupplied},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="prePurchaseCategory != null">pre_purchase_category = #{prePurchaseCategory},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePrePurchasedNotOrderedById" parameterType="Long">
        delete from mdl_pre_purchased_not_ordered where id = #{id}
    </delete>

    <delete id="deletePrePurchasedNotOrderedByIds" parameterType="String">
        delete from mdl_pre_purchased_not_ordered where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePrePurchasedNotOrderedByDataAnalysisId" parameterType="Long">
        delete from mdl_pre_purchased_not_ordered where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>