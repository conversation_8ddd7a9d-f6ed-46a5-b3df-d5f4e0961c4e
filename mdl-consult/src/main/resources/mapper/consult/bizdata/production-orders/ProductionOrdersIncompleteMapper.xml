<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.ProductionOrdersIncompleteMapper">

    <resultMap type="ProductionOrdersIncomplete" id="ProductionOrdersIncompleteResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="documentDate"    column="document_date"    />
        <result property="productionOrderNo"    column="production_order_no"    />
        <result property="customerName"    column="customer_name"    />
        <result property="salesOrderNo"    column="sales_order_no"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="specification"    column="specification"    />
        <result property="transactionType"    column="transaction_type"    />
        <result property="schedulingTime"    column="scheduling_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="productionQuantity"    column="production_quantity"    />
        <result property="cumulativeStorage"    column="cumulative_storage"    />
        <result property="unstoredQuantity"    column="unstored_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectProductionOrdersIncompleteVo">
        select a.id, a.data_analysis_id, a.document_date, a.production_order_no, a.customer_name, a.sales_order_no, a.material_code, a.material_name, a.specification, a.transaction_type, a.scheduling_time, a.start_time, a.order_status, a.production_quantity, a.cumulative_storage, a.unstored_quantity, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_production_orders_incomplete a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectProductionOrdersIncompleteList" parameterType="ProductionOrdersIncomplete" resultMap="ProductionOrdersIncompleteResult">
        <include refid="selectProductionOrdersIncompleteVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="documentDate != null "> and a.document_date = #{documentDate}</if>
            <if test="productionOrderNo != null  and productionOrderNo != ''"> and a.production_order_no = #{productionOrderNo}</if>
            <if test="customerName != null  and customerName != ''"> and a.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="salesOrderNo != null  and salesOrderNo != ''"> and a.sales_order_no = #{salesOrderNo}</if>
            <if test="materialCode != null  and materialCode != ''"> and a.material_code = #{materialCode}</if>
            <if test="materialName != null  and materialName != ''"> and a.material_name like concat('%', #{materialName}, '%')</if>
            <if test="specification != null  and specification != ''"> and a.specification = #{specification}</if>
            <if test="transactionType != null  and transactionType != ''"> and a.transaction_type = #{transactionType}</if>
            <if test="schedulingTime != null "> and a.scheduling_time = #{schedulingTime}</if>
            <if test="startTime != null "> and a.start_time = #{startTime}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and a.order_status = #{orderStatus}</if>
            <if test="productionQuantity != null "> and a.production_quantity = #{productionQuantity}</if>
            <if test="cumulativeStorage != null "> and a.cumulative_storage = #{cumulativeStorage}</if>
            <if test="unstoredQuantity != null "> and a.unstored_quantity = #{unstoredQuantity}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectProductionOrdersIncompleteById" parameterType="Long" resultMap="ProductionOrdersIncompleteResult">
        <include refid="selectProductionOrdersIncompleteVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertProductionOrdersIncomplete" parameterType="ProductionOrdersIncomplete" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_production_orders_incomplete
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="documentDate != null">document_date,</if>
            <if test="productionOrderNo != null">production_order_no,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="salesOrderNo != null">sales_order_no,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="specification != null">specification,</if>
            <if test="transactionType != null">transaction_type,</if>
            <if test="schedulingTime != null">scheduling_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="productionQuantity != null">production_quantity,</if>
            <if test="cumulativeStorage != null">cumulative_storage,</if>
            <if test="unstoredQuantity != null">unstored_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="documentDate != null">#{documentDate},</if>
            <if test="productionOrderNo != null">#{productionOrderNo},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="salesOrderNo != null">#{salesOrderNo},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="transactionType != null">#{transactionType},</if>
            <if test="schedulingTime != null">#{schedulingTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="productionQuantity != null">#{productionQuantity},</if>
            <if test="cumulativeStorage != null">#{cumulativeStorage},</if>
            <if test="unstoredQuantity != null">#{unstoredQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProductionOrdersIncomplete" parameterType="ProductionOrdersIncomplete">
        update mdl_production_orders_incomplete
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="documentDate != null">document_date = #{documentDate},</if>
            <if test="productionOrderNo != null">production_order_no = #{productionOrderNo},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="salesOrderNo != null">sales_order_no = #{salesOrderNo},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="transactionType != null">transaction_type = #{transactionType},</if>
            <if test="schedulingTime != null">scheduling_time = #{schedulingTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="productionQuantity != null">production_quantity = #{productionQuantity},</if>
            <if test="cumulativeStorage != null">cumulative_storage = #{cumulativeStorage},</if>
            <if test="unstoredQuantity != null">unstored_quantity = #{unstoredQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductionOrdersIncompleteById" parameterType="Long">
        delete from mdl_production_orders_incomplete where id = #{id}
    </delete>

    <delete id="deleteProductionOrdersIncompleteByIds" parameterType="String">
        delete from mdl_production_orders_incomplete where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProductionOrdersIncompleteByDataAnalysisId" parameterType="Long">
        delete from mdl_production_orders_incomplete where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>