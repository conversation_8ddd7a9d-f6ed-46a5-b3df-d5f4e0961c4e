<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.MaterialsWorkshopReceivedMapper">

    <resultMap type="MaterialsWorkshopReceived" id="MaterialsWorkshopReceivedResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="warehouse"    column="warehouse"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialName"    column="material_name"    />
        <result property="specification"    column="specification"    />
        <result property="currentQuantity"    column="current_quantity"    />
        <result property="availableQuantity"    column="available_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectMaterialsWorkshopReceivedVo">
        select a.id, a.data_analysis_id, a.warehouse, a.material_code, a.material_name, a.specification, a.current_quantity, a.available_quantity, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_materials_workshop_received a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectMaterialsWorkshopReceivedList" parameterType="MaterialsWorkshopReceived" resultMap="MaterialsWorkshopReceivedResult">
        <include refid="selectMaterialsWorkshopReceivedVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="warehouse != null  and warehouse != ''"> and a.warehouse = #{warehouse}</if>
            <if test="materialCode != null  and materialCode != ''"> and a.material_code = #{materialCode}</if>
            <if test="materialName != null  and materialName != ''"> and a.material_name like concat('%', #{materialName}, '%')</if>
            <if test="specification != null  and specification != ''"> and a.specification = #{specification}</if>
            <if test="currentQuantity != null "> and a.current_quantity = #{currentQuantity}</if>
            <if test="availableQuantity != null "> and a.available_quantity = #{availableQuantity}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectMaterialsWorkshopReceivedById" parameterType="Long" resultMap="MaterialsWorkshopReceivedResult">
        <include refid="selectMaterialsWorkshopReceivedVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertMaterialsWorkshopReceived" parameterType="MaterialsWorkshopReceived" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_materials_workshop_received
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="warehouse != null">warehouse,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialName != null">material_name,</if>
            <if test="specification != null">specification,</if>
            <if test="currentQuantity != null">current_quantity,</if>
            <if test="availableQuantity != null">available_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="warehouse != null">#{warehouse},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="currentQuantity != null">#{currentQuantity},</if>
            <if test="availableQuantity != null">#{availableQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMaterialsWorkshopReceived" parameterType="MaterialsWorkshopReceived">
        update mdl_materials_workshop_received
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="warehouse != null">warehouse = #{warehouse},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="currentQuantity != null">current_quantity = #{currentQuantity},</if>
            <if test="availableQuantity != null">available_quantity = #{availableQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialsWorkshopReceivedById" parameterType="Long">
        delete from mdl_materials_workshop_received where id = #{id}
    </delete>

    <delete id="deleteMaterialsWorkshopReceivedByIds" parameterType="String">
        delete from mdl_materials_workshop_received where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMaterialsWorkshopReceivedByDataAnalysisId" parameterType="Long">
        delete from mdl_materials_workshop_received where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>