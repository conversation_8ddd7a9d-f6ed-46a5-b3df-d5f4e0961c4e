<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.bizdata.mapper.SalesOrdersIncompleteMapper">

    <resultMap type="SalesOrdersIncomplete" id="SalesOrdersIncompleteResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="orderDate"    column="order_date"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="salesOrderNo"    column="sales_order_no"    />
        <result property="productionOrderNo"    column="production_order_no"    />
        <result property="customerName"    column="customer_name"    />
        <result property="inventoryCode"    column="inventory_code"    />
        <result property="inventoryName"    column="inventory_name"    />
        <result property="specification"    column="specification"    />
        <result property="unit"    column="unit"    />
        <result property="quantity"    column="quantity"    />
        <result property="cumulativeShipment"    column="cumulative_shipment"    />
        <result property="unshippedQuantity"    column="unshipped_quantity"    />
        <result property="safeQuantity"    column="safe_quantity"    />
        <result property="currentMonthProduction"    column="current_month_production"    />
        <result property="currentBalance"    column="current_balance"    />
        <result property="monthlyProductionRequired"    column="monthly_production_required"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="statisticsNumber" column="statistics_number" />
    </resultMap>

    <sql id="selectSalesOrdersIncompleteVo">
        select a.id, a.data_analysis_id, a.order_date, a.delivery_date, a.sales_order_no, a.production_order_no, a.customer_name, a.inventory_code, a.inventory_name, a.specification, a.unit, a.quantity, a.cumulative_shipment, a.unshipped_quantity, a.safe_quantity, a.current_month_production, a.current_balance,a.monthly_production_required, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, d.statistics_number
        from mdl_sales_orders_incomplete a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectSalesOrdersIncompleteList" parameterType="SalesOrdersIncomplete" resultMap="SalesOrdersIncompleteResult">
        <include refid="selectSalesOrdersIncompleteVo"/>
        <where>
            <if test="dataAnalysisId != null "> and a.data_analysis_id = #{dataAnalysisId}</if>
            <if test="orderDate != null "> and a.order_date = #{orderDate}</if>
            <if test="deliveryDate != null "> and a.delivery_date = #{deliveryDate}</if>
            <if test="salesOrderNo != null  and salesOrderNo != ''"> and a.sales_order_no = #{salesOrderNo}</if>
            <if test="productionOrderNo != null  and productionOrderNo != ''"> and a.production_order_no = #{productionOrderNo}</if>
            <if test="customerName != null  and customerName != ''"> and a.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="inventoryCode != null  and inventoryCode != ''"> and a.inventory_code = #{inventoryCode}</if>
            <if test="inventoryName != null  and inventoryName != ''"> and a.inventory_name like concat('%', #{inventoryName}, '%')</if>
            <if test="specification != null  and specification != ''"> and a.specification = #{specification}</if>
            <if test="unit != null  and unit != ''"> anda. unit = #{unit}</if>
            <if test="quantity != null "> and a.quantity = #{quantity}</if>
            <if test="cumulativeShipment != null "> and a.cumulative_shipment = #{cumulativeShipment}</if>
            <if test="unshippedQuantity != null "> and a.unshipped_quantity = #{unshippedQuantity}</if>
            <if test="safeQuantity != null "> and a.safe_quantity = #{safeQuantity}</if>
            <if test="currentMonthProduction != null "> and a.current_month_production = #{currentMonthProduction}</if>
            <if test="currentBalance != null "> and a.current_balance = #{currentBalance}</if>
            <if test="monthlyProductionRequired != null "> and a.monthly_production_required = #{monthlyProductionRequired}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
        </where>
    </select>

    <select id="selectSalesOrdersIncompleteById" parameterType="Long" resultMap="SalesOrdersIncompleteResult">
        <include refid="selectSalesOrdersIncompleteVo"/>
        where id = #{id}
    </select>

    <insert id="insertSalesOrdersIncomplete" parameterType="SalesOrdersIncomplete" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_sales_orders_incomplete
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="salesOrderNo != null">sales_order_no,</if>
            <if test="productionOrderNo != null">production_order_no,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="inventoryCode != null">inventory_code,</if>
            <if test="inventoryName != null">inventory_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unit != null">unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="cumulativeShipment != null">cumulative_shipment,</if>
            <if test="unshippedQuantity != null">unshipped_quantity,</if>
            <if test="safeQuantity != null">safe_quantity,</if>
            <if test="currentMonthProduction != null">current_month_production,</if>
            <if test="currentBalance != null">current_balance,</if>
            <if test="monthlyProductionRequired != null">monthly_production_required,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="salesOrderNo != null">#{salesOrderNo},</if>
            <if test="productionOrderNo != null">#{productionOrderNo},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="inventoryCode != null">#{inventoryCode},</if>
            <if test="inventoryName != null">#{inventoryName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unit != null">#{unit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="cumulativeShipment != null">#{cumulativeShipment},</if>
            <if test="unshippedQuantity != null">#{unshippedQuantity},</if>
            <if test="safeQuantity != null">#{safeQuantity},</if>
            <if test="currentMonthProduction != null">#{currentMonthProduction},</if>
            <if test="currentBalance != null">#{currentBalance},</if>
            <if test="monthlyProductionRequired != null">#{monthlyProductionRequired},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSalesOrdersIncomplete" parameterType="SalesOrdersIncomplete">
        update mdl_sales_orders_incomplete
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="salesOrderNo != null">sales_order_no = #{salesOrderNo},</if>
            <if test="productionOrderNo != null">production_order_no = #{productionOrderNo},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="inventoryCode != null">inventory_code = #{inventoryCode},</if>
            <if test="inventoryName != null">inventory_name = #{inventoryName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="cumulativeShipment != null">cumulative_shipment = #{cumulativeShipment},</if>
            <if test="unshippedQuantity != null">unshipped_quantity = #{unshippedQuantity},</if>
            <if test="safeQuantity != null">safe_quantity = #{safeQuantity},</if>
            <if test="currentMonthProduction != null">current_month_production = #{currentMonthProduction},</if>
            <if test="currentBalance != null">current_balance = #{currentBalance},</if>
            <if test="monthlyProductionRequired != null">monthly_production_required = #{monthlyProductionRequired},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSalesOrdersIncompleteById" parameterType="Long">
        delete from mdl_sales_orders_incomplete where id = #{id}
    </delete>

    <delete id="deleteSalesOrdersIncompleteByIds" parameterType="String">
        delete from mdl_sales_orders_incomplete where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSalesOrdersIncompleteByDataAnalysisId" parameterType="Long">
        delete from mdl_sales_orders_incomplete where data_analysis_id = #{dataAnalysisId}
    </delete>

</mapper>