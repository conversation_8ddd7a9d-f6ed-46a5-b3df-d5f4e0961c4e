<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.caldata.mapper.CalOrderRequiredMapper">

    <resultMap type="CalOrderRequired" id="CalOrderRequiredResult">
        <result property="id"    column="id"    />
        <result property="dataAnalysisId"    column="data_analysis_id"    />
        <result property="statisticsNumber" column="statistics_number" />
        <result property="inventoryCode"    column="inventory_code"    />
        <result property="inventoryName"    column="inventory_name"    />
        <result property="specification"    column="specification"    />
        <result property="unit"    column="unit"    />
        <result property="quantity"    column="quantity"    />
        <result property="cumulativeShipment"    column="cumulative_shipment"    />
        <result property="unshippedQuantity"    column="unshipped_quantity"    />
        <result property="currentBalance"    column="current_balance"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="monthlyProductionRequired"    column="monthly_production_required"    />
        <result property="safeQuantity"    column="safe_quantity"    />
    </resultMap>

    <sql id="selectCalOrderRequiredVo">
        select a.id, a.data_analysis_id,d.statistics_number, a.inventory_code, a.inventory_name, a.specification, a.unit, a.quantity, a.cumulative_shipment, a.unshipped_quantity, a.current_balance, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,a.monthly_production_required, a.safe_quantity
        from mdl_cal_order_required a
        left join mdl_data_analysis d on a.data_analysis_id = d.id
    </sql>

    <select id="selectCalOrderRequiredList" parameterType="CalOrderRequired" resultMap="CalOrderRequiredResult">
        <include refid="selectCalOrderRequiredVo"/>
        <where>
            <if test="dataAnalysisId != null "> and data_analysis_id = #{dataAnalysisId}</if>
            <if test="statisticsNumber != null and statisticsNumber != ''"> and d.statistics_number = #{statisticsNumber}</if>
            <if test="inventoryCode != null  and inventoryCode != ''"> and inventory_code = #{inventoryCode}</if>
            <if test="inventoryName != null  and inventoryName != ''"> and inventory_name like concat('%', #{inventoryName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="cumulativeShipment != null "> and cumulative_shipment = #{cumulativeShipment}</if>
            <if test="unshippedQuantity != null "> and unshipped_quantity = #{unshippedQuantity}</if>
            <if test="currentBalance != null "> and current_balance = #{currentBalance}</if>
            <if test="monthlyProductionRequired != null "> and monthly_production_required = #{monthlyProductionRequired}</if>
            <if test="safeQuantity != null "> and safe_quantity = #{safeQuantity}</if>
        </where>
    </select>

    <select id="selectCalOrderRequiredById" parameterType="Long" resultMap="CalOrderRequiredResult">
        <include refid="selectCalOrderRequiredVo"/>
        where id = #{id}
    </select>

    <insert id="insertCalOrderRequired" parameterType="CalOrderRequired" useGeneratedKeys="true" keyProperty="id">
        insert into mdl_cal_order_required
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id,</if>
            <if test="inventoryCode != null">inventory_code,</if>
            <if test="inventoryName != null">inventory_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unit != null">unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="cumulativeShipment != null">cumulative_shipment,</if>
            <if test="unshippedQuantity != null">unshipped_quantity,</if>
            <if test="currentBalance != null">current_balance,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="monthlyProductionRequired != null">monthly_production_required,</if>
            <if test="safeQuantity != null">safe_quantity,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataAnalysisId != null">#{dataAnalysisId},</if>
            <if test="inventoryCode != null">#{inventoryCode},</if>
            <if test="inventoryName != null">#{inventoryName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unit != null">#{unit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="cumulativeShipment != null">#{cumulativeShipment},</if>
            <if test="unshippedQuantity != null">#{unshippedQuantity},</if>
            <if test="currentBalance != null">#{currentBalance},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="monthlyProductionRequired != null">#{monthlyProductionRequired},</if>
            <if test="safeQuantity != null">#{safeQuantity},</if>
        </trim>
    </insert>

    <update id="updateCalOrderRequired" parameterType="CalOrderRequired">
        update mdl_cal_order_required
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataAnalysisId != null">data_analysis_id = #{dataAnalysisId},</if>
            <if test="inventoryCode != null">inventory_code = #{inventoryCode},</if>
            <if test="inventoryName != null">inventory_name = #{inventoryName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="cumulativeShipment != null">cumulative_shipment = #{cumulativeShipment},</if>
            <if test="unshippedQuantity != null">unshipped_quantity = #{unshippedQuantity},</if>
            <if test="currentBalance != null">current_balance = #{currentBalance},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="monthlyProductionRequired != null">monthly_production_required = #{monthlyProductionRequired},</if>
            <if test="safeQuantity != null">safe_quantity = #{safeQuantity},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCalOrderRequiredById" parameterType="Long">
        delete from mdl_cal_order_required where id = #{id}
    </delete>

    <delete id="deleteCalOrderRequiredByIds" parameterType="String">
        delete from mdl_cal_order_required where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delByDataAnalysisId" parameterType="Long">
        delete from mdl_cal_order_required where data_analysis_id = #{dataAnalysisId}
    </delete>

    <select id="sumOrderRequired" resultType="Map">
        select t.inventory_code,safeStock.inventory_name,safeStock.specification,safeStock.unit,safeStock.safety_stock,orders.quantity,orders.cumulative_shipment,stock.current_month_balance
        from (
            select inventory_code
            from mdl_safety_stock_finished_product
            where data_analysis_id = #{dataAnalysisId}
            union
            select inventory_code
            from mdl_sales_orders_incomplete
            where data_analysis_id = #{dataAnalysisId}) t
        <!-- 安全库存数量 -->
        left join (
            select inventory_code,ANY_VALUE(inventory_name) inventory_name,ANY_VALUE(specification) specification,ANY_VALUE(unit) unit,sum(safety_stock) safety_stock
            from mdl_safety_stock_finished_product
            where data_analysis_id = #{dataAnalysisId}
            group by inventory_code
        ) safeStock on safeStock.inventory_code = t.inventory_code
        <!-- 订单数量 -->
        left join (
            select inventory_code,sum(quantity) quantity,sum(cumulative_shipment) cumulative_shipment
            from mdl_sales_orders_incomplete
            where data_analysis_id = #{dataAnalysisId}
            group by inventory_code
        ) orders on orders.inventory_code = t.inventory_code
        <!-- 成品库数量 -->
        left join (
            select material_code,sum(COALESCE(current_month_balance,0)) current_month_balance
            from mdl_inventory_goods
            where data_analysis_id = #{dataAnalysisId} and warehouse_type = 1
            group by material_code
        ) stock on stock.material_code = t.inventory_code
    </select>

</mapper>