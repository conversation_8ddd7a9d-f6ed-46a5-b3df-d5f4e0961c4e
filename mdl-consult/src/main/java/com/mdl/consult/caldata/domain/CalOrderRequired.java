package com.mdl.consult.caldata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单需求对象 mdl_cal_order_required
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public class CalOrderRequired extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    private String statisticsNumber;

    /** 存货编码 */
    @Excel(name = "存货编码")
    private String inventoryCode;

    /** 存货名称 */
    @Excel(name = "存货名称")
    private String inventoryName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 数量 */
    @Excel(name = "订单数量")
    private BigDecimal quantity;

    /** 安全库存 */
    @Excel(name = "安全库存")
    private BigDecimal safeQuantity;

    /** 现在结存 */
    @Excel(name = "现在结存")
    private BigDecimal currentBalance;

    /** 累计发货数量 */
    @Excel(name = "累计发货数量")
    private BigDecimal cumulativeShipment;

    /** 未发货数量 */
    @Excel(name = "未发货数量")
    private BigDecimal unshippedQuantity;

    /** 本月应生产数 */
    @Excel(name = "本月应生产数")
    private BigDecimal monthlyProductionRequired;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId)
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId()
    {
        return dataAnalysisId;
    }

    public void setInventoryCode(String inventoryCode)
    {
        this.inventoryCode = inventoryCode;
    }

    public String getInventoryCode()
    {
        return inventoryCode;
    }

    public void setInventoryName(String inventoryName)
    {
        this.inventoryName = inventoryName;
    }

    public String getInventoryName()
    {
        return inventoryName;
    }

    public void setSpecification(String specification)
    {
        this.specification = specification;
    }

    public String getSpecification()
    {
        return specification;
    }

    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }

    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }

    public void setCumulativeShipment(BigDecimal cumulativeShipment)
    {
        this.cumulativeShipment = cumulativeShipment;
    }

    public BigDecimal getCumulativeShipment()
    {
        return cumulativeShipment;
    }

    public void setUnshippedQuantity(BigDecimal unshippedQuantity)
    {
        this.unshippedQuantity = unshippedQuantity;
    }

    public BigDecimal getUnshippedQuantity()
    {
        return unshippedQuantity;
    }

    public void setCurrentBalance(BigDecimal currentBalance)
    {
        this.currentBalance = currentBalance;
    }

    public BigDecimal getCurrentBalance()
    {
        return currentBalance;
    }

    public void setMonthlyProductionRequired(BigDecimal monthlyProductionRequired)
    {
        this.monthlyProductionRequired = monthlyProductionRequired;
    }

    public BigDecimal getMonthlyProductionRequired()
    {
        return monthlyProductionRequired;
    }

    public void setSafeQuantity(BigDecimal safeQuantity)
    {
        this.safeQuantity = safeQuantity;
    }

    public BigDecimal getSafeQuantity()
    {
        return safeQuantity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("dataAnalysisId", getDataAnalysisId())
                .append("inventoryCode", getInventoryCode())
                .append("inventoryName", getInventoryName())
                .append("specification", getSpecification())
                .append("unit", getUnit())
                .append("quantity", getQuantity())
                .append("cumulativeShipment", getCumulativeShipment())
                .append("unshippedQuantity", getUnshippedQuantity())
                .append("currentBalance", getCurrentBalance())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("monthlyProductionRequired", getMonthlyProductionRequired())
                .append("safeQuantity", getSafeQuantity())
                .toString();
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }
}
