package com.mdl.consult.caldata.service;

import java.util.List;
import com.mdl.consult.caldata.domain.CalOrderRequired;

/**
 * 订单需求Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ICalOrderRequiredService
{
    /**
     * 查询订单需求
     *
     * @param id 订单需求主键
     * @return 订单需求
     */
    public CalOrderRequired selectCalOrderRequiredById(Long id);

    /**
     * 查询订单需求列表
     *
     * @param calOrderRequired 订单需求
     * @return 订单需求集合
     */
    public List<CalOrderRequired> selectCalOrderRequiredList(CalOrderRequired calOrderRequired);

    /**
     * 新增订单需求
     *
     * @param calOrderRequired 订单需求
     * @return 结果
     */
    public int insertCalOrderRequired(CalOrderRequired calOrderRequired);

    /**
     * 修改订单需求
     *
     * @param calOrderRequired 订单需求
     * @return 结果
     */
    public int updateCalOrderRequired(CalOrderRequired calOrderRequired);

    /**
     * 批量删除订单需求
     *
     * @param ids 需要删除的订单需求主键集合
     * @return 结果
     */
    public int deleteCalOrderRequiredByIds(String ids);

    /**
     * 删除订单需求信息
     *
     * @param id 订单需求主键
     * @return 结果
     */
    public int deleteCalOrderRequiredById(Long id);
}
