package com.mdl.consult.caldata.mapper;

import java.util.List;
import java.util.Map;

import com.mdl.consult.caldata.domain.CalOrderRequired;

/**
 * 订单需求Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface CalOrderRequiredMapper
{
    /**
     * 查询订单需求
     *
     * @param id 订单需求主键
     * @return 订单需求
     */
    public CalOrderRequired selectCalOrderRequiredById(Long id);

    /**
     * 查询订单需求列表
     *
     * @param calOrderRequired 订单需求
     * @return 订单需求集合
     */
    public List<CalOrderRequired> selectCalOrderRequiredList(CalOrderRequired calOrderRequired);

    /**
     * 新增订单需求
     *
     * @param calOrderRequired 订单需求
     * @return 结果
     */
    public int insertCalOrderRequired(CalOrderRequired calOrderRequired);

    /**
     * 修改订单需求
     *
     * @param calOrderRequired 订单需求
     * @return 结果
     */
    public int updateCalOrderRequired(CalOrderRequired calOrderRequired);

    /**
     * 删除订单需求
     *
     * @param id 订单需求主键
     * @return 结果
     */
    public int deleteCalOrderRequiredById(Long id);

    /**
     * 批量删除订单需求
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCalOrderRequiredByIds(String[] ids);

    int delByDataAnalysisId(Long dataAnalysisId);

    List<Map> sumOrderRequired(Long dataAnalysisId);
}
