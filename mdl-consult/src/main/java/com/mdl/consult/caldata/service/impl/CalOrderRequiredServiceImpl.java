package com.mdl.consult.caldata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.DataSumMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.caldata.mapper.CalOrderRequiredMapper;
import com.mdl.consult.caldata.domain.CalOrderRequired;
import com.mdl.consult.caldata.service.ICalOrderRequiredService;
import com.ruoyi.common.core.text.Convert;

/**
 * 订单需求Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
@Slf4j
public class CalOrderRequiredServiceImpl implements ICalOrderRequiredService
{
    @Autowired
    private CalOrderRequiredMapper calOrderRequiredMapper;
    @Autowired
    private DataSumMapper materialShortageAnalysisMapper;

    /**
     * 查询订单需求
     *
     * @param id 订单需求主键
     * @return 订单需求
     */
    @Override
    public CalOrderRequired selectCalOrderRequiredById(Long id)
    {
        return calOrderRequiredMapper.selectCalOrderRequiredById(id);
    }

    /**
     * 查询订单需求列表
     *
     * @param calOrderRequired 订单需求
     * @return 订单需求
     */
    @Override
    public List<CalOrderRequired> selectCalOrderRequiredList(CalOrderRequired calOrderRequired)
    {
        return calOrderRequiredMapper.selectCalOrderRequiredList(calOrderRequired);
    }

    /**
     * 新增订单需求
     *
     * @param calOrderRequired 订单需求
     * @return 结果
     */
    @Override
    public int insertCalOrderRequired(CalOrderRequired calOrderRequired)
    {
        calOrderRequired.setCreateTime(DateUtils.getNowDate());
        return calOrderRequiredMapper.insertCalOrderRequired(calOrderRequired);
    }

    /**
     * 修改订单需求
     *
     * @param calOrderRequired 订单需求
     * @return 结果
     */
    @Override
    public int updateCalOrderRequired(CalOrderRequired calOrderRequired)
    {
        calOrderRequired.setUpdateTime(DateUtils.getNowDate());
        return calOrderRequiredMapper.updateCalOrderRequired(calOrderRequired);
    }

    /**
     * 批量删除订单需求
     *
     * @param ids 需要删除的订单需求主键
     * @return 结果
     */
    @Override
    public int deleteCalOrderRequiredByIds(String ids)
    {
        return calOrderRequiredMapper.deleteCalOrderRequiredByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除订单需求信息
     *
     * @param id 订单需求主键
     * @return 结果
     */
    @Override
    public int deleteCalOrderRequiredById(Long id)
    {
        return calOrderRequiredMapper.deleteCalOrderRequiredById(id);
    }

    public void calData(Long dataAnalysisId) {
        calOrderRequiredMapper.delByDataAnalysisId(dataAnalysisId);

        List<Map> sumOrderRequired = calOrderRequiredMapper.sumOrderRequired(dataAnalysisId);
        for (Map map : sumOrderRequired) {
            CalOrderRequired orderRequired = new CalOrderRequired();
            orderRequired.setDataAnalysisId(dataAnalysisId);
            orderRequired.setInventoryCode(MapUtils.getString(map,"inventory_code"));
            orderRequired.setInventoryName(MapUtils.getString(map,"inventory_name"));
            orderRequired.setSpecification(MapUtils.getString(map,"specification"));
            orderRequired.setUnit(MapUtils.getString(map,"unit"));
            orderRequired.setQuantity(new BigDecimal(MapUtils.getString(map,"quantity","0")));
            orderRequired.setCumulativeShipment(new BigDecimal(MapUtils.getString(map,"cumulative_shipment","0")));
            orderRequired.setCurrentBalance(new BigDecimal(MapUtils.getString(map,"current_month_balance","0")));
            orderRequired.setSafeQuantity(new BigDecimal(MapUtils.getString(map,"safety_stock","0")));

            BigDecimal quantity = Objects.requireNonNullElse(orderRequired.getQuantity(),new BigDecimal(0));
            /** 累计发货数量 */
            BigDecimal cumulativeShipment = Objects.requireNonNullElse(orderRequired.getCumulativeShipment(),new BigDecimal(0));
            /** 未发货数量 */
            BigDecimal unshippedQuantity = quantity.subtract(cumulativeShipment);

            BigDecimal currentBalance = Objects.requireNonNullElse(orderRequired.getCurrentBalance(),new BigDecimal(0));
            BigDecimal safeQuantity = Objects.requireNonNullElse(orderRequired.getSafeQuantity(),new BigDecimal(0));

            orderRequired.setUnshippedQuantity(unshippedQuantity);

            BigDecimal orderQuantity = unshippedQuantity.add(safeQuantity);
            BigDecimal subtractBalance;
            if(orderQuantity.compareTo(currentBalance) > 0){
                subtractBalance = currentBalance;
            }else{
                subtractBalance = orderQuantity;
            }
            orderRequired.setMonthlyProductionRequired(orderQuantity.subtract(subtractBalance));

            insertCalOrderRequired(orderRequired);
        }
    }
}
