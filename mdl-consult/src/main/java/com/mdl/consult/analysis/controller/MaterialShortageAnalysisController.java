package com.mdl.consult.analysis.controller;

import java.util.List;

import com.mdl.consult.analysis.service.IImportTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.analysis.domain.MaterialShortageSummary;
import com.mdl.consult.analysis.mapper.DataSumMapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 欠料分析Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@RestController
@RequestMapping("/consult/analysis/material-shortage")
public class MaterialShortageAnalysisController extends BaseController
{
    @Autowired
    private DataSumMapper materialShortageAnalysisMapper;
    @Autowired
    private IImportTaskService importTaskService;

    /**
     * 查询欠料分析列表
     */
    @RequiresPermissions("analysis:material-shortage:list")
    @PostMapping("/list")
    public TableDataInfo list(MaterialShortageSummary materialShortageSummary)
    {
        startPage();

        boolean existTaskDoing = importTaskService.existTaskDoing(materialShortageSummary.getStatisticsNumber());
        if (existTaskDoing) {
            throw new RuntimeException("当前有导入任务正在进行中，请稍后再试");
        }

        // 使用SQL直接计算欠料分析数据
        List<MaterialShortageSummary> calculatedList = materialShortageAnalysisMapper.calculateMaterialShortage(materialShortageSummary);

        // 直接返回计算结果
        return getDataTable(calculatedList);
    }

    /**
     * 导出欠料分析列表
     */
    @RequiresPermissions("analysis:material-shortage:export")
    @Log(title = "欠料分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(MaterialShortageSummary materialShortageSummary)
    {
        // 使用SQL直接计算欠料分析数据
        List<MaterialShortageSummary> list = materialShortageAnalysisMapper.calculateMaterialShortage(materialShortageSummary);

        ExcelUtil<MaterialShortageSummary> util = new ExcelUtil<>(MaterialShortageSummary.class);
        return util.exportExcel(list, "欠料分析数据");
    }


}
