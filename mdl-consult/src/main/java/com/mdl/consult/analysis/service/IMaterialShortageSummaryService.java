package com.mdl.consult.analysis.service;

import java.util.List;
import com.mdl.consult.analysis.domain.MaterialShortageSummary;

/**
 * 欠料汇总Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface IMaterialShortageSummaryService 
{
    /**
     * 查询欠料汇总
     * 
     * @param id 欠料汇总主键
     * @return 欠料汇总
     */
    public MaterialShortageSummary selectMaterialShortageSummaryById(Long id);

    /**
     * 查询欠料汇总列表
     * 
     * @param materialShortageSummary 欠料汇总
     * @return 欠料汇总集合
     */
    public List<MaterialShortageSummary> selectMaterialShortageSummaryList(MaterialShortageSummary materialShortageSummary);

    /**
     * 新增欠料汇总
     * 
     * @param materialShortageSummary 欠料汇总
     * @return 结果
     */
    public int insertMaterialShortageSummary(MaterialShortageSummary materialShortageSummary);

    /**
     * 修改欠料汇总
     * 
     * @param materialShortageSummary 欠料汇总
     * @return 结果
     */
    public int updateMaterialShortageSummary(MaterialShortageSummary materialShortageSummary);

    /**
     * 批量删除欠料汇总
     * 
     * @param ids 需要删除的欠料汇总主键集合
     * @return 结果
     */
    public int deleteMaterialShortageSummaryByIds(String ids);

    /**
     * 删除欠料汇总信息
     * 
     * @param id 欠料汇总主键
     * @return 结果
     */
    public int deleteMaterialShortageSummaryById(Long id);
}
