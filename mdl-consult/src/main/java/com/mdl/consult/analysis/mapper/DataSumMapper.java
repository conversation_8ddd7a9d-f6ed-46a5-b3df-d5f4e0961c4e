package com.mdl.consult.analysis.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.mdl.consult.analysis.domain.MaterialShortageSummary;

/**
 * 欠料分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface DataSumMapper
{
    /**
     * 计算欠料分析数据
     *
     * @return 欠料分析数据列表
     */
    List<MaterialShortageSummary> calculateMaterialShortage(MaterialShortageSummary queryParam);

    /**
     * 销售生产需求数
     * @param inventoryCode
     * @return
     */
    BigDecimal getSaleProductRequired(Long dataAnalysisId, String inventoryCode, String salesOrderNo);

    /**
     * 在途数
     * @param dataAnalysisId
     * @param materialCode
     * @return
     */
    BigDecimal getInProcess(Long dataAnalysisId, String materialCode);

    /**
     * 委外数
     * @param dataAnalysisId
     * @param materialCode
     * @return
     */
    BigDecimal getOutsourced(Long dataAnalysisId, String materialCode);

    /**
     * 车间已领数
     * @param dataAnalysisId
     * @param materialCode
     * @return
     */
    BigDecimal getWorkshopReceived(Long dataAnalysisId, String materialCode);

    /**
     * 库存数(成品、半成品)
     * @param materialCode
     * @return
     */
    BigDecimal getInventory(Integer warehouseType,Long dataAnalysisId,String materialCode);

    /**
     * 成品安全库存
     * @param dataAnalysisId
     * @param productCode
     * @return
     */
    BigDecimal getProductSafeInventory(Long dataAnalysisId, String productCode);

    /**
     * 物料安全库存
     * @param dataAnalysisId
     * @param materialCode
     * @return
     */
    BigDecimal getMaterialSafeInventory(Long dataAnalysisId, String materialCode);

    /**
     * 半成品需求数
     * @param parentCode
     * @return
     */
    BigDecimal getSemifinished(Long dataAnalysisId, String parentCode);

}
