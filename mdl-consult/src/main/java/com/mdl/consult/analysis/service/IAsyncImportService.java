package com.mdl.consult.analysis.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 异步导入服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IAsyncImportService 
{
    /**
     * 异步导入BOM数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportBom(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入半成品BOM数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportBomSemifinished(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入包装BOM数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportBomPackaging(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入产成品安全库存数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportSafetyStockFinishedProduct(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入物料安全库存数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportSafetyStockMaterial(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入产成品对应半成品数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportProductSemifinished(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入库存商品数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportInventoryGoods(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入未完成销售订单数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportSalesOrdersIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入未完成委外订单数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportOrdersOutsourcingIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入已预购未下采购单数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportPrePurchasedNotOrdered(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入需生产的销售单数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportSalesOrdersForProduction(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入车间已领数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportMaterialsWorkshopReceived(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入未完成采购单数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportPurchaseOrdersIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
    
    /**
     * 异步导入未完成生产单数据
     * 
     * @param file 导入文件
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param updateSupport 是否支持更新
     * @return 任务ID
     */
    Long asyncImportProductionOrdersIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport);
}
