package com.mdl.consult.analysis.service;

import java.util.List;
import com.mdl.consult.analysis.domain.SalesOrdersProductionCheck;

/**
 * 未完成的销售订单（与生产订单核对）Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface ISalesOrdersProductionCheckService 
{
    /**
     * 查询未完成的销售订单（与生产订单核对）
     * 
     * @param id 未完成的销售订单（与生产订单核对）主键
     * @return 未完成的销售订单（与生产订单核对）
     */
    public SalesOrdersProductionCheck selectSalesOrdersProductionCheckById(Long id);

    /**
     * 查询未完成的销售订单（与生产订单核对）列表
     * 
     * @param salesOrdersProductionCheck 未完成的销售订单（与生产订单核对）
     * @return 未完成的销售订单（与生产订单核对）集合
     */
    public List<SalesOrdersProductionCheck> selectSalesOrdersProductionCheckList(SalesOrdersProductionCheck salesOrdersProductionCheck);

    /**
     * 新增未完成的销售订单（与生产订单核对）
     * 
     * @param salesOrdersProductionCheck 未完成的销售订单（与生产订单核对）
     * @return 结果
     */
    public int insertSalesOrdersProductionCheck(SalesOrdersProductionCheck salesOrdersProductionCheck);

    /**
     * 修改未完成的销售订单（与生产订单核对）
     * 
     * @param salesOrdersProductionCheck 未完成的销售订单（与生产订单核对）
     * @return 结果
     */
    public int updateSalesOrdersProductionCheck(SalesOrdersProductionCheck salesOrdersProductionCheck);

    /**
     * 批量删除未完成的销售订单（与生产订单核对）
     * 
     * @param ids 需要删除的未完成的销售订单（与生产订单核对）主键集合
     * @return 结果
     */
    public int deleteSalesOrdersProductionCheckByIds(String ids);

    /**
     * 删除未完成的销售订单（与生产订单核对）信息
     * 
     * @param id 未完成的销售订单（与生产订单核对）主键
     * @return 结果
     */
    public int deleteSalesOrdersProductionCheckById(Long id);
}
