package com.mdl.consult.analysis.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.ruoyi.common.utils.file.FileUploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.mdl.consult.analysis.service.IAsyncImportService;
import com.mdl.consult.analysis.service.IImportTaskService;
import com.mdl.consult.basedata.controller.BomController;
import com.mdl.consult.basedata.controller.BomSemifinishedController;
import com.mdl.consult.basedata.controller.BomPackagingController;
import com.mdl.consult.basedata.controller.SafetyStockFinishedProductController;
import com.mdl.consult.basedata.controller.SafetyStockMaterialController;
import com.mdl.consult.basedata.controller.ProductSemifinishedController;
import com.mdl.consult.bizdata.controller.InventoryGoodsController;
import com.mdl.consult.bizdata.controller.SalesOrdersIncompleteController;
import com.mdl.consult.bizdata.controller.OrdersOutsourcingIncompleteController;
import com.mdl.consult.bizdata.controller.PrePurchasedNotOrderedController;
import com.mdl.consult.bizdata.controller.SalesOrdersForProductionController;
import com.mdl.consult.bizdata.controller.MaterialsWorkshopReceivedController;
import com.mdl.consult.bizdata.controller.PurchaseOrdersIncompleteController;
import com.mdl.consult.bizdata.controller.ProductionOrdersIncompleteController;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 异步导入服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
@Slf4j
public class AsyncImportServiceImpl implements IAsyncImportService
{
    @Autowired
    private IImportTaskService importTaskService;

    @Autowired
    private BomController bomController;

    @Autowired
    private BomSemifinishedController bomSemifinishedController;

    @Autowired
    private BomPackagingController bomPackagingController;

    @Autowired
    private SafetyStockFinishedProductController safetyStockFinishedProductController;

    @Autowired
    private SafetyStockMaterialController safetyStockMaterialController;

    @Autowired
    private ProductSemifinishedController productSemifinishedController;

    @Autowired
    private InventoryGoodsController inventoryGoodsController;

    @Autowired
    private SalesOrdersIncompleteController salesOrdersIncompleteController;

    @Autowired
    private OrdersOutsourcingIncompleteController ordersOutsourcingIncompleteController;

    @Autowired
    private PrePurchasedNotOrderedController prePurchasedNotOrderedController;

    @Autowired
    private SalesOrdersForProductionController salesOrdersForProductionController;

    @Autowired
    private MaterialsWorkshopReceivedController materialsWorkshopReceivedController;

    @Autowired
    private PurchaseOrdersIncompleteController purchaseOrdersIncompleteController;

    @Autowired
    private ProductionOrdersIncompleteController productionOrdersIncompleteController;
    @Autowired@Lazy
    private DataAnalysisServiceImpl dataAnalysisService;

    /**
     * 异步导入BOM数据
     */
    @Override
    public Long asyncImportBom(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "Bom");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.basedata.domain.Bom> util = new ExcelUtil<>(com.mdl.consult.basedata.domain.Bom.class);
                List<com.mdl.consult.basedata.domain.Bom> bomList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = bomController.importBom(dataAnalysisId, bomList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入半成品BOM数据
     */
    @Override
    public Long asyncImportBomSemifinished(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "BomSemifinished");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.basedata.domain.BomSemifinished> util = new ExcelUtil<>(com.mdl.consult.basedata.domain.BomSemifinished.class);
                List<com.mdl.consult.basedata.domain.BomSemifinished> bomSemifinishedList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = bomSemifinishedController.importBomSemifinished(dataAnalysisId, bomSemifinishedList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    private String uploadFile(MultipartFile file) {
        try {
            return FileUploadUtils.upload(file).getAbsPath();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 异步导入包装BOM数据
     */
    @Override
    public Long asyncImportBomPackaging(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "BomPackaging");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.basedata.domain.BomPackaging> util = new ExcelUtil<>(com.mdl.consult.basedata.domain.BomPackaging.class);
                List<com.mdl.consult.basedata.domain.BomPackaging> bomPackagingList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = bomPackagingController.importBomPackaging(dataAnalysisId, bomPackagingList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入产成品安全库存数据
     */
    @Override
    public Long asyncImportSafetyStockFinishedProduct(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "SafetyStockFinishedProduct");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.basedata.domain.SafetyStockFinishedProduct> util = new ExcelUtil<>(com.mdl.consult.basedata.domain.SafetyStockFinishedProduct.class);
                List<com.mdl.consult.basedata.domain.SafetyStockFinishedProduct> safetyStockFinishedProductList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = safetyStockFinishedProductController.importSafetyStockFinishedProduct(dataAnalysisId, safetyStockFinishedProductList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入物料安全库存数据
     */
    @Override
    public Long asyncImportSafetyStockMaterial(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "SafetyStockMaterial");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.basedata.domain.SafetyStockMaterial> util = new ExcelUtil<>(com.mdl.consult.basedata.domain.SafetyStockMaterial.class);
                List<com.mdl.consult.basedata.domain.SafetyStockMaterial> safetyStockMaterialList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = safetyStockMaterialController.importSafetyStockMaterial(dataAnalysisId, safetyStockMaterialList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入产成品对应半成品数据
     */
    @Override
    public Long asyncImportProductSemifinished(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "ProductSemifinished");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.basedata.domain.ProductSemifinished> util = new ExcelUtil<>(com.mdl.consult.basedata.domain.ProductSemifinished.class);
                List<com.mdl.consult.basedata.domain.ProductSemifinished> productSemifinishedList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = productSemifinishedController.importProductSemifinished(dataAnalysisId, productSemifinishedList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入库存商品数据
     */
    @Override
    public Long asyncImportInventoryGoods(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "InventoryGoods");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.InventoryGoods> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.InventoryGoods.class);
                List<com.mdl.consult.bizdata.domain.InventoryGoods> inventoryGoodsList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = inventoryGoodsController.importInventoryGoods(dataAnalysisId, inventoryGoodsList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入未完成销售订单数据
     */
    @Override
    public Long asyncImportSalesOrdersIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "SalesOrdersIncomplete");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.SalesOrdersIncomplete> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.SalesOrdersIncomplete.class);
                List<com.mdl.consult.bizdata.domain.SalesOrdersIncomplete> salesOrdersIncompleteList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = salesOrdersIncompleteController.importSalesOrdersIncomplete(dataAnalysisId, salesOrdersIncompleteList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入未完成委外订单数据
     */
    @Override
    public Long asyncImportOrdersOutsourcingIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "OrdersOutsourcingIncomplete");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete.class);
                List<com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete> ordersOutsourcingIncompleteList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = ordersOutsourcingIncompleteController.importOrdersOutsourcingIncomplete(dataAnalysisId, ordersOutsourcingIncompleteList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入已预购未下采购单数据
     */
    @Override
    public Long asyncImportPrePurchasedNotOrdered(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "PrePurchasedNotOrdered");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered.class);
                List<com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered> prePurchasedNotOrderedList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = prePurchasedNotOrderedController.importPrePurchasedNotOrdered(dataAnalysisId, prePurchasedNotOrderedList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入需生产的销售单数据
     */
    @Override
    public Long asyncImportSalesOrdersForProduction(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "SalesOrdersForProduction");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.SalesOrdersForProduction> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.SalesOrdersForProduction.class);
                List<com.mdl.consult.bizdata.domain.SalesOrdersForProduction> salesOrdersForProductionList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = salesOrdersForProductionController.importSalesOrdersForProduction(dataAnalysisId, salesOrdersForProductionList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入车间已领数据
     */
    @Override
    public Long asyncImportMaterialsWorkshopReceived(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "MaterialsWorkshopReceived");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived.class);
                List<com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived> materialsWorkshopReceivedList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = materialsWorkshopReceivedController.importMaterialsWorkshopReceived(dataAnalysisId, materialsWorkshopReceivedList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入未完成采购单数据
     */
    @Override
    public Long asyncImportPurchaseOrdersIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "PurchaseOrdersIncomplete");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete.class);
                List<com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete> purchaseOrdersIncompleteList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = purchaseOrdersIncompleteController.importPurchaseOrdersIncomplete(dataAnalysisId, purchaseOrdersIncompleteList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入未完成生产单数据
     */
    @Override
    public Long asyncImportProductionOrdersIncomplete(MultipartFile file, Long dataAnalysisId, String statisticsNumber, boolean updateSupport)
    {
        // 创建导入任务
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "ProductionOrdersIncomplete");

        // 异步执行导入
        String uploadFilePath = uploadFile(file);
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为导入中
                importTaskService.updateTaskToImporting(taskId);

                // 解析Excel文件
                ExcelUtil<com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete> util = new ExcelUtil<>(com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete.class);
                List<com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete> productionOrdersIncompleteList = util.importExcel(new FileInputStream(uploadFilePath));

                // 执行导入逻辑
                String message = productionOrdersIncompleteController.importProductionOrdersIncomplete(dataAnalysisId, productionOrdersIncompleteList, updateSupport);

                // 解析导入结果
                parseImportResult(taskId, message);

            } catch (Exception e) {
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 异步导入车间已领数据
     */
    public Long asyncCalData(Long dataAnalysisId, String statisticsNumber)
    {
        Long taskId = importTaskService.createImportTask(dataAnalysisId, statisticsNumber, "CalData");

        CompletableFuture.runAsync(() -> {
            try {
                importTaskService.updateTaskToImporting(taskId);
                log.info("计算数据开始,dataAnalysisId:{}",dataAnalysisId);
                dataAnalysisService.calData(dataAnalysisId);
                // 解析导入结果
                parseImportResult(taskId, "计算数据成功");
                log.info("计算数据结束,dataAnalysisId:{}",dataAnalysisId);

            } catch (Exception e) {
                log.error("计算数据异常,dataAnalysisId:{}",dataAnalysisId,e);
                // 更新任务状态为失败
                importTaskService.updateTaskToFailed(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    /**
     * 解析导入结果
     */
    private void parseImportResult(Long taskId, String message) {
        try {
            // 解析成功和失败数量
            Long successCount = 0L;
            Long failureCount = 0L;

            if (message.contains("成功")) {
                // 提取成功数量
                String[] parts = message.split("共 ");
                if (parts.length > 1) {
                    String countPart = parts[1].split(" 条")[0];
                    try {
                        successCount = Long.parseLong(countPart);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
            }

            if (message.contains("失败")) {
                // 提取失败数量
                String[] parts = message.split("失败！共 ");
                if (parts.length > 1) {
                    String countPart = parts[1].split(" 条")[0];
                    try {
                        failureCount = Long.parseLong(countPart);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
            }

            // 更新任务状态为已完成
            importTaskService.updateTaskToCompleted(taskId, successCount, failureCount, StringUtils.abbreviate(message,2048));

        } catch (Exception e) {
            // 如果解析失败，仍然标记为完成，但记录错误
            importTaskService.updateTaskToCompleted(taskId, 0L, 0L, "导入完成，但结果解析失败：" + e.getMessage());
        }
    }
}
