package com.mdl.consult.analysis.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mdl.consult.analysis.service.IImportTaskService;
import com.mdl.consult.basedata.domain.BomPackaging;
import com.mdl.consult.basedata.domain.BomSemifinished;
import com.mdl.consult.basedata.domain.ProductSemifinished;
import com.mdl.consult.basedata.domain.SafetyStockFinishedProduct;
import com.mdl.consult.basedata.service.IBomPackagingService;
import com.mdl.consult.basedata.service.IBomSemifinishedService;
import com.mdl.consult.basedata.service.IProductSemifinishedService;
import com.mdl.consult.basedata.service.ISafetyStockFinishedProductService;
import com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete;
import com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete;
import com.mdl.consult.bizdata.domain.SalesOrdersForProduction;
import com.mdl.consult.bizdata.domain.SalesOrdersIncomplete;
import com.mdl.consult.bizdata.service.IOrdersOutsourcingIncompleteService;
import com.mdl.consult.bizdata.service.IProductionOrdersIncompleteService;
import com.mdl.consult.bizdata.service.ISalesOrdersForProductionService;
import com.mdl.consult.bizdata.service.ISalesOrdersIncompleteService;
import com.mdl.consult.caldata.service.impl.CalOrderRequiredServiceImpl;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.analysis.mapper.DataAnalysisMapper;
import com.mdl.consult.analysis.domain.DataAnalysis;
import com.mdl.consult.analysis.service.IDataAnalysisService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.constant.UserConstants;

/**
 * 数据分析Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
@Slf4j
public class DataAnalysisServiceImpl implements IDataAnalysisService
{
    @Autowired
    private DataAnalysisMapper dataAnalysisMapper;
    @Autowired
    private ISalesOrdersForProductionService salesOrdersForProductionService;
    @Autowired
    private IProductSemifinishedService productSemifinishedService;
    @Autowired
    private IBomSemifinishedService bomSemifinishedService;
    @Autowired
    private IBomPackagingService bomPackagingService;
    @Autowired
    private ISalesOrdersIncompleteService salesOrdersIncompleteService;
    @Autowired
    private IProductionOrdersIncompleteService productionOrdersIncompleteService;
    @Autowired
    private ISafetyStockFinishedProductService safetyStockProductService;
    @Autowired
    private AsyncImportServiceImpl asyncImportService;
    @Autowired
    private IImportTaskService importTaskService;

    /**
     * 查询数据分析
     *
     * @param id 数据分析主键
     * @return 数据分析
     */
    @Override
    public DataAnalysis selectDataAnalysisById(Long id)
    {
        return dataAnalysisMapper.selectDataAnalysisById(id);
    }

    /**
     * 查询数据分析列表
     *
     * @param dataAnalysis 数据分析
     * @return 数据分析
     */
    @Override
    public List<DataAnalysis> selectDataAnalysisList(DataAnalysis dataAnalysis)
    {
        List<DataAnalysis> dataAnalyses = dataAnalysisMapper.selectDataAnalysisList(dataAnalysis);
        supplement(dataAnalyses);
        return dataAnalyses;
    }

    /**
     * 新增数据分析
     *
     * @param dataAnalysis 数据分析
     * @return 结果
     */
    @Override
    public int insertDataAnalysis(DataAnalysis dataAnalysis)
    {
        dataAnalysis.setCreateTime(DateUtils.getNowDate());
        return dataAnalysisMapper.insertDataAnalysis(dataAnalysis);
    }

    /**
     * 修改数据分析
     *
     * @param dataAnalysis 数据分析
     * @return 结果
     */
    @Override
    public int updateDataAnalysis(DataAnalysis dataAnalysis)
    {
        dataAnalysis.setUpdateTime(DateUtils.getNowDate());
        int updtNum = dataAnalysisMapper.updateDataAnalysis(dataAnalysis);
        asyncImportService.asyncCalData(dataAnalysis.getId(),dataAnalysis.getStatisticsNumber());
        return updtNum;
    }

    /**
     * 批量删除数据分析
     *
     * @param ids 需要删除的数据分析主键
     * @return 结果
     */
    @Override
    public int deleteDataAnalysisByIds(String ids)
    {
        return dataAnalysisMapper.deleteDataAnalysisByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除数据分析信息
     *
     * @param id 数据分析主键
     * @return 结果
     */
    @Override
    public int deleteDataAnalysisById(Long id)
    {
        return dataAnalysisMapper.deleteDataAnalysisById(id);
    }

    /**
     * 校验统计编号是否唯一
     *
     * @param dataAnalysis 数据分析信息
     * @return 结果
     */
    @Override
    public boolean checkStatisticsNumberUnique(DataAnalysis dataAnalysis)
    {
        Long id = StringUtils.isNull(dataAnalysis.getId()) ? -1L : dataAnalysis.getId();
        DataAnalysis info = dataAnalysisMapper.checkStatisticsNumberUnique(dataAnalysis.getStatisticsNumber());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void calData(Long dataAnalysisId) {
        //1、库存商品数
        //2、需生产的销售单
        SalesOrdersForProduction q = new SalesOrdersForProduction();
        q.setDataAnalysisId(dataAnalysisId);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (SalesOrdersForProduction salesOrdersForProduction : salesOrdersForProductionService.selectSalesOrdersForProductionList(q)) {
            futures.add(CompletableFuture.runAsync(()->{
                salesOrdersForProductionService.calData(salesOrdersForProduction);
                salesOrdersForProductionService.updateSalesOrdersForProduction(salesOrdersForProduction);
            }));
        }
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allTasks.join();

        //2、计算订单需求
        SalesOrdersIncomplete q4 = new SalesOrdersIncomplete();
        q4.setDataAnalysisId(dataAnalysisId);
        futures = new ArrayList<>();
        List<SalesOrdersIncomplete> ordersIncompletes = salesOrdersIncompleteService.selectSalesOrdersIncompleteList(q4);

        SafetyStockFinishedProduct qstock = new SafetyStockFinishedProduct();
        qstock.setDataAnalysisId(dataAnalysisId);
        List<SafetyStockFinishedProduct> safetyStockFinishedProducts = safetyStockProductService.selectSafetyStockFinishedProductList(qstock);
        List<String> salesBomcodes = ordersIncompletes.stream().map(SalesOrdersIncomplete::getInventoryCode).toList();
        List<SalesOrdersIncomplete> filterStock = safetyStockFinishedProducts
                .stream()
                .filter(stock -> !salesBomcodes.contains(stock.getInventoryCode()))
                .map(stock -> {
                    SalesOrdersIncomplete order = new SalesOrdersIncomplete();
                    order.setDataAnalysisId(dataAnalysisId);
                    //order.setOrderDate();
                    //order.setDeliveryDate();
                    //order.setSalesOrderNo();
                    //order.setProductionOrderNo();
                    //order.setCustomerName("from-safety-stock");
                    order.getParams().put("isNew",true);
                    order.setInventoryCode(stock.getInventoryCode());
                    order.setInventoryName(stock.getInventoryName());
                    order.setSpecification(stock.getSpecification());
                    order.setUnit(stock.getUnit());
                    //order.setQuantity(new BigDecimal(0));
                    //order.setCumulativeShipment(new BigDecimal(0));
                    //order.setUnshippedQuantity(new BigDecimal(0));
                    order.setSafeQuantity(stock.getSafetyStock());
                    //order.setCurrentMonthProduction();
                    //order.setCurrentBalance();
                    //order.setMonthlyProductionRequired();
                    return order;
                }).toList();

        if(!filterStock.isEmpty()){
            ordersIncompletes.addAll(filterStock);
        }

        Map<String,List<SalesOrdersIncomplete>> ordersIncompletesMap = ordersIncompletes.stream().collect(Collectors.groupingBy(SalesOrdersIncomplete::getInventoryCode));
        for (Map.Entry<String, List<SalesOrdersIncomplete>> ordersIncompleteEntry : ordersIncompletesMap.entrySet()) {
            futures.add(CompletableFuture.runAsync(()->{
                salesOrdersIncompleteService.calData(ordersIncompleteEntry.getKey(),ordersIncompleteEntry.getValue());
            }));
        }
        allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allTasks.join();

        //3、产成品对应半成品
        ProductSemifinished q1 = new ProductSemifinished();
        q1.setDataAnalysisId(dataAnalysisId);
        futures = new ArrayList<>();
        for (ProductSemifinished semifinished : productSemifinishedService.selectProductSemifinishedList(q1)) {
            futures.add(CompletableFuture.runAsync(()->{
                productSemifinishedService.calData(semifinished);
                productSemifinishedService.updateProductSemifinished(semifinished);
            }));
        }
        allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allTasks.join();

        //4、半成品BOM
        BomSemifinished q2 = new BomSemifinished();
        q2.setDataAnalysisId(dataAnalysisId);
        futures = new ArrayList<>();
        for (BomSemifinished bomSemifinished : bomSemifinishedService.selectBomSemifinishedList(q2)) {
            futures.add(CompletableFuture.runAsync(()->{
                bomSemifinishedService.calData(bomSemifinished);
                bomSemifinishedService.updateBomSemifinished(bomSemifinished);
            }));
        }
        allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allTasks.join();

        //5、包装BOM
        BomPackaging q3 = new BomPackaging();
        q3.setDataAnalysisId(dataAnalysisId);
        futures = new ArrayList<>();
        for (BomPackaging bomPackaging : bomPackagingService.selectBomPackagingList(q3)) {
            futures.add(CompletableFuture.runAsync(()->{
                bomPackagingService.calData(bomPackaging);
                bomPackagingService.updateBomPackaging(bomPackaging);
            }));
        }
        allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allTasks.join();

        //7、未完成生产单
        ProductionOrdersIncomplete q5 = new ProductionOrdersIncomplete();
        q5.setDataAnalysisId(dataAnalysisId);
        futures = new ArrayList<>();
        for (ProductionOrdersIncomplete productionOrdersIncomplete : productionOrdersIncompleteService.selectProductionOrdersIncompleteList(q5)) {
            futures.add(CompletableFuture.runAsync(()->{
                productionOrdersIncompleteService.calData(productionOrdersIncomplete);
                productionOrdersIncompleteService.updateProductionOrdersIncomplete(productionOrdersIncomplete);
            }));
        }
        allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allTasks.join();

        //BOM
        //产成品安全库存
        //物料安全库存
        //未完成委外订单
        /*OrdersOutsourcingIncomplete q6 = new OrdersOutsourcingIncomplete();
        q6.setDataAnalysisId(dataAnalysisId);
        for (OrdersOutsourcingIncomplete ordersOutsourcingIncomplete : ordersOutsourcingIncompleteService.selectOrdersOutsourcingIncompleteList(q6)) {
            ordersOutsourcingIncompleteService.calData(ordersOutsourcingIncomplete);
            ordersOutsourcingIncompleteService.updateOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete);
        }*/
        //已预购未下采购单
        //车间已领
        //未完成采购单
    }

    private void supplement(List<DataAnalysis> dataAnalyses) {
        for (DataAnalysis dataAnalysis : dataAnalyses) {
            supplement(dataAnalysis);
        }
    }

    private void supplement(DataAnalysis dataAnalysis) {
        boolean existTaskDoing = importTaskService.existTaskDoing(dataAnalysis.getId());

        if(existTaskDoing){
            dataAnalysis.setTaskStatus("正在计算中，请稍后...");
        }else{
            dataAnalysis.setTaskStatus("");
        }
    }
}
