package com.mdl.consult.analysis.enums;

/**
 * 导入任务状态枚举
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public enum ImportTaskStatus
{
    /**
     * 等待中
     */
    PENDING("0", "等待中"),
    
    /**
     * 导入中
     */
    IMPORTING("1", "导入中"),
    
    /**
     * 已完成
     */
    COMPLETED("2", "已完成"),
    
    /**
     * 失败
     */
    FAILED("3", "失败");

    private final String code;
    private final String info;

    ImportTaskStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    
    public static ImportTaskStatus getByCode(String code)
    {
        for (ImportTaskStatus status : values())
        {
            if (status.getCode().equals(code))
            {
                return status;
            }
        }
        return null;
    }
}
