package com.mdl.consult.analysis.controller;

import java.util.List;

import com.mdl.consult.analysis.service.IImportTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.analysis.domain.SalesOrdersProductionCheck;
import com.mdl.consult.analysis.mapper.SalesComparisonMapper;
import com.mdl.consult.analysis.service.IDataAnalysisService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 销售对比Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@RestController
@RequestMapping("/consult/analysis/sales-comparison")
public class SalesComparisonController extends BaseController
{

    @Autowired
    private SalesComparisonMapper salesComparisonMapper;
    @Autowired
    private IImportTaskService importTaskService;

    /**
     * 查询销售对比列表
     */
    @RequiresPermissions("analysis:sales-comparison:list")
    @PostMapping("/list")
    public TableDataInfo list(SalesOrdersProductionCheck salesOrdersProductionCheck)
    {
        startPage();
        boolean existTaskDoing = importTaskService.existTaskDoing(salesOrdersProductionCheck.getStatisticsNumber());
        if (existTaskDoing) {
            throw new RuntimeException("当前有导入任务正在进行中，请稍后再试");
        }

        // 使用SQL直接查询销售对比数据
        List<SalesOrdersProductionCheck> list = salesComparisonMapper.selectSalesComparisonList(salesOrdersProductionCheck);

        // 直接返回查询结果
        return getDataTable(list);
    }

    /**
     * 导出销售对比列表
     */
    @RequiresPermissions("analysis:sales-comparison:export")
    @Log(title = "销售对比", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(SalesOrdersProductionCheck salesOrdersProductionCheck)
    {
        // 使用SQL直接查询销售对比数据
        List<SalesOrdersProductionCheck> list = salesComparisonMapper.selectSalesComparisonList(salesOrdersProductionCheck);

        ExcelUtil<SalesOrdersProductionCheck> util = new ExcelUtil<>(SalesOrdersProductionCheck.class);
        return util.exportExcel(list, "销售对比数据");
    }
}
