package com.mdl.consult.analysis.mapper;

import java.util.List;
import com.mdl.consult.analysis.domain.ImportTask;
import org.apache.ibatis.annotations.Param;

/**
 * 导入任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ImportTaskMapper 
{
    /**
     * 查询导入任务
     * 
     * @param taskId 导入任务主键
     * @return 导入任务
     */
    public ImportTask selectImportTaskByTaskId(Long taskId);

    /**
     * 查询导入任务列表
     * 
     * @param importTask 导入任务
     * @return 导入任务集合
     */
    public List<ImportTask> selectImportTaskList(ImportTask importTask);

    /**
     * 新增导入任务
     * 
     * @param importTask 导入任务
     * @return 结果
     */
    public int insertImportTask(ImportTask importTask);

    /**
     * 修改导入任务
     * 
     * @param importTask 导入任务
     * @return 结果
     */
    public int updateImportTask(ImportTask importTask);

    /**
     * 删除导入任务
     * 
     * @param taskId 导入任务主键
     * @return 结果
     */
    public int deleteImportTaskByTaskId(Long taskId);

    /**
     * 批量删除导入任务
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteImportTaskByTaskIds(String[] taskIds);
    
    /**
     * 根据数据分析ID和导入类型查询最新的导入任务
     * 
     * @param dataAnalysisId 数据分析ID
     * @param importType 导入类型
     * @return 导入任务
     */
    public ImportTask selectLatestImportTask(@Param("dataAnalysisId") Long dataAnalysisId, @Param("importType") String importType);
}
