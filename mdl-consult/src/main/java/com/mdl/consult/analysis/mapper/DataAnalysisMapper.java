package com.mdl.consult.analysis.mapper;

import java.util.List;
import com.mdl.consult.analysis.domain.DataAnalysis;

/**
 * 数据分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface DataAnalysisMapper
{
    /**
     * 查询数据分析
     *
     * @param id 数据分析主键
     * @return 数据分析
     */
    public DataAnalysis selectDataAnalysisById(Long id);

    /**
     * 查询数据分析列表
     *
     * @param dataAnalysis 数据分析
     * @return 数据分析集合
     */
    public List<DataAnalysis> selectDataAnalysisList(DataAnalysis dataAnalysis);

    /**
     * 新增数据分析
     *
     * @param dataAnalysis 数据分析
     * @return 结果
     */
    public int insertDataAnalysis(DataAnalysis dataAnalysis);

    /**
     * 修改数据分析
     *
     * @param dataAnalysis 数据分析
     * @return 结果
     */
    public int updateDataAnalysis(DataAnalysis dataAnalysis);

    /**
     * 删除数据分析
     *
     * @param id 数据分析主键
     * @return 结果
     */
    public int deleteDataAnalysisById(Long id);

    /**
     * 批量删除数据分析
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataAnalysisByIds(String[] ids);

    /**
     * 校验统计编号是否唯一
     *
     * @param statisticsNumber 统计编号
     * @return 结果
     */
    public DataAnalysis checkStatisticsNumberUnique(String statisticsNumber);
}
