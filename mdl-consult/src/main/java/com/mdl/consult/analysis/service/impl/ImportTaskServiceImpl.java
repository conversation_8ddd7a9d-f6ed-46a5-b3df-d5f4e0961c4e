package com.mdl.consult.analysis.service.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import com.mdl.consult.analysis.enums.ImportTaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.analysis.mapper.ImportTaskMapper;
import com.mdl.consult.analysis.domain.ImportTask;
import com.mdl.consult.analysis.service.IImportTaskService;
import com.ruoyi.common.core.text.Convert;

/**
 * 导入任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class ImportTaskServiceImpl implements IImportTaskService 
{
    @Autowired
    private ImportTaskMapper importTaskMapper;

    /**
     * 查询导入任务
     * 
     * @param taskId 导入任务主键
     * @return 导入任务
     */
    @Override
    public ImportTask selectImportTaskByTaskId(Long taskId)
    {
        return importTaskMapper.selectImportTaskByTaskId(taskId);
    }

    /**
     * 查询导入任务列表
     * 
     * @param importTask 导入任务
     * @return 导入任务
     */
    @Override
    public List<ImportTask> selectImportTaskList(ImportTask importTask)
    {
        return importTaskMapper.selectImportTaskList(importTask);
    }

    /**
     * 新增导入任务
     * 
     * @param importTask 导入任务
     * @return 结果
     */
    @Override
    public int insertImportTask(ImportTask importTask)
    {
        importTask.setCreateTime(DateUtils.getNowDate());
        return importTaskMapper.insertImportTask(importTask);
    }

    /**
     * 修改导入任务
     * 
     * @param importTask 导入任务
     * @return 结果
     */
    @Override
    public int updateImportTask(ImportTask importTask)
    {
        importTask.setUpdateTime(DateUtils.getNowDate());
        return importTaskMapper.updateImportTask(importTask);
    }

    /**
     * 批量删除导入任务
     * 
     * @param taskIds 需要删除的导入任务主键
     * @return 结果
     */
    @Override
    public int deleteImportTaskByTaskIds(String taskIds)
    {
        return importTaskMapper.deleteImportTaskByTaskIds(Convert.toStrArray(taskIds));
    }

    /**
     * 删除导入任务信息
     * 
     * @param taskId 导入任务主键
     * @return 结果
     */
    @Override
    public int deleteImportTaskByTaskId(Long taskId)
    {
        return importTaskMapper.deleteImportTaskByTaskId(taskId);
    }
    
    /**
     * 根据数据分析ID和导入类型查询最新的导入任务
     * 
     * @param dataAnalysisId 数据分析ID
     * @param importType 导入类型
     * @return 导入任务
     */
    @Override
    public ImportTask selectLatestImportTask(Long dataAnalysisId, String importType)
    {
        return importTaskMapper.selectLatestImportTask(dataAnalysisId, importType);
    }
    
    /**
     * 创建导入任务
     * 
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param importType 导入类型
     * @return 任务ID
     */
    @Override
    public Long createImportTask(Long dataAnalysisId, String statisticsNumber, String importType)
    {
        ImportTask task = new ImportTask();
        task.setDataAnalysisId(dataAnalysisId);
        task.setStatisticsNumber(statisticsNumber);
        task.setImportType(importType);
        task.setStatus(ImportTaskStatus.PENDING.getCode());
        task.setCreateTime(new Date());
        
        importTaskMapper.insertImportTask(task);
        return task.getTaskId();
    }
    
    /**
     * 更新任务状态为导入中
     * 
     * @param taskId 任务ID
     */
    @Override
    public void updateTaskToImporting(Long taskId)
    {
        ImportTask task = new ImportTask();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatus.IMPORTING.getCode());
        task.setStartTime(new Date());
        task.setUpdateTime(new Date());
        
        importTaskMapper.updateImportTask(task);
    }
    
    /**
     * 更新任务状态为已完成
     * 
     * @param taskId 任务ID
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param resultMessage 结果信息
     */
    @Override
    public void updateTaskToCompleted(Long taskId, Long successCount, Long failureCount, String resultMessage)
    {
        ImportTask task = new ImportTask();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatus.COMPLETED.getCode());
        task.setEndTime(new Date());
        task.setSuccessCount(successCount);
        task.setFailureCount(failureCount);
        task.setResultMessage(resultMessage);
        task.setUpdateTime(new Date());
        
        importTaskMapper.updateImportTask(task);
    }
    
    /**
     * 更新任务状态为失败
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    @Override
    public void updateTaskToFailed(Long taskId, String errorMessage)
    {
        ImportTask task = new ImportTask();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatus.FAILED.getCode());
        task.setEndTime(new Date());
        task.setErrorMessage(errorMessage);
        task.setUpdateTime(new Date());
        
        importTaskMapper.updateImportTask(task);
    }

    @Override
    public boolean existTaskDoing(Long taskId) {
        // 检查当前是否有正在进行的导入任务
        ImportTask q = new ImportTask();
        q.setDataAnalysisId(taskId);
        q.setStatus("1");
        List<ImportTask> existingTask = selectImportTaskList(q);

        return !existingTask.isEmpty();
    }

    @Override
    public boolean existTaskDoing(String statisticsNumber) {
        // 检查当前是否有正在进行的导入任务
        ImportTask q = new ImportTask();
        q.setStatisticsNumber(statisticsNumber);
        q.setStatus("1");
        List<ImportTask> existingTask = selectImportTaskList(q);

        return !existingTask.isEmpty();
    }
}
