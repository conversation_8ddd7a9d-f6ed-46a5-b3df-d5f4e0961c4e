package com.mdl.consult.analysis.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 导入任务对象 mdl_import_task
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public class ImportTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号 */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 导入类型 */
    @Excel(name = "导入类型")
    private String importType;

    /** 任务状态（0等待中 1导入中 2已完成 3失败） */
    @Excel(name = "任务状态", readConverterExp = "0=等待中,1=导入中,2=已完成,3=失败")
    private String status;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 成功数量 */
    @Excel(name = "成功数量")
    private Long successCount;

    /** 失败数量 */
    @Excel(name = "失败数量")
    private Long failureCount;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 结果信息 */
    @Excel(name = "结果信息")
    private String resultMessage;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    
    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }
    
    public void setStatisticsNumber(String statisticsNumber) 
    {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() 
    {
        return statisticsNumber;
    }
    
    public void setImportType(String importType) 
    {
        this.importType = importType;
    }

    public String getImportType() 
    {
        return importType;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    
    public void setSuccessCount(Long successCount) 
    {
        this.successCount = successCount;
    }

    public Long getSuccessCount() 
    {
        return successCount;
    }
    
    public void setFailureCount(Long failureCount) 
    {
        this.failureCount = failureCount;
    }

    public Long getFailureCount() 
    {
        return failureCount;
    }
    
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    
    public void setResultMessage(String resultMessage) 
    {
        this.resultMessage = resultMessage;
    }

    public String getResultMessage() 
    {
        return resultMessage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("importType", getImportType())
            .append("status", getStatus())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("successCount", getSuccessCount())
            .append("failureCount", getFailureCount())
            .append("errorMessage", getErrorMessage())
            .append("resultMessage", getResultMessage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
