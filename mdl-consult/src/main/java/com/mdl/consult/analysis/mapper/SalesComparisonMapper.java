package com.mdl.consult.analysis.mapper;

import java.util.List;
import com.mdl.consult.analysis.domain.SalesOrdersProductionCheck;

/**
 * 销售对比Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface SalesComparisonMapper
{
    /**
     * 查询销售对比数据列表
     *
     * @param salesOrdersProductionCheck 查询条件
     * @return 销售对比数据列表
     */
    List<SalesOrdersProductionCheck> selectSalesComparisonList(SalesOrdersProductionCheck salesOrdersProductionCheck);
}
