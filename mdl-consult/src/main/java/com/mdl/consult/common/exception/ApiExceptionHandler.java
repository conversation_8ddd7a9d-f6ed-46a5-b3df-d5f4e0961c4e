package com.mdl.consult.common.exception;

import com.mdl.consult.common.entity.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
@Slf4j
public class ApiExceptionHandler {
    @ExceptionHandler(ApiException.class)
    public ResponseEntity<ApiResponse<String>> handleApiExceptionException(ApiException ex, HttpServletRequest request) {
        ApiResponse<String> response = ApiResponse.failure(ex.getStatusCode(), ex.getMessage());
        return new ResponseEntity<>(response, HttpStatusCode.valueOf(ex.getStatusCode()));
    }

    @ExceptionHandler(MyBatisSystemException.class)
    public ResponseEntity<ApiResponse<String>> handleMyBatisSystemExceptionException(MyBatisSystemException ex, HttpServletRequest request) {
        ApiResponse<String> response = null;
        if(ex.getCause().getClass().equals(TooManyResultsException.class)){
            response = ApiResponse.failure(ApiException.BAD_REQUEST, "数据查询异常，存在多条数据");
        }else{
            log.error("数据处理异常",ex);
            response = ApiResponse.failure(ApiException.BAD_REQUEST, "数据处理异常:"+ex.getMessage());
        }

        return new ResponseEntity<>(response, HttpStatusCode.valueOf(response.getCode()));
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<String>> handleMissingParameterException(MissingServletRequestParameterException ex, HttpServletRequest request) {
        ApiResponse<String> response = ApiResponse.failure(ApiException.FORM_INVALID, "缺少必要的参数:"+ex.getMessage());
        return new ResponseEntity<>(response, HttpStatusCode.valueOf(ApiException.FORM_INVALID));
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<String>> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        ApiResponse<String> response = ApiResponse.failure(ApiException.FORM_INVALID, "访问错误，请检查接口请求方式:"+ex.getMessage());
        return new ResponseEntity<>(response, HttpStatusCode.valueOf(ApiException.BAD_REQUEST));
    }

    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiResponse<String>> handleIllegalStateException(IllegalStateException ex, HttpServletRequest request) {
        log.error("系统异常",ex);
        ApiResponse<String> response = ApiResponse.failure(ApiException.BAD_REQUEST, ex.getMessage());
        return new ResponseEntity<>(response, HttpStatusCode.valueOf(response.getCode()));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<String>> handleException(Exception ex, HttpServletRequest request) {
        log.error("系统异常",ex);
        ApiResponse<String> response = ApiResponse.failure(ApiException.SYSTEM, "系统异常");
        return new ResponseEntity<>(response, HttpStatusCode.valueOf(response.getCode()));
    }
}
