package com.mdl.consult.common.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  会员登录成功的session标识信息
 * <AUTHOR>
 * @date 12/8/20
 */
@Data
public class UserAccessInfo implements Serializable {
    public final static String header_token_key = "x-token";
    public final static String header_appid_Key = "x-appid";
    public final static String header_url_Key = "x-url";

    /**
     * 访问来源App
     */
    //private ThirdApp thirdApp;

    /**
     * 当前 token
     */
    private String token;
    /**
     * 访问来源AppId
     */
    private String appid;
    /**
     * 访问来源用户id
     */
    private String openId;
    /**
     * 当前登录的公司
     */
    private String copCode;
    /**
     * 师傅ID
     */
    private String technicianId;
    /**
     * 登录时间
     */
    private Date loginTime;
}
