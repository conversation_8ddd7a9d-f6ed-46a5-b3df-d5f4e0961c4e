package com.mdl.consult.common.entity;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.mdl.consult.common.tools.ParamUtil;

public interface EnumStringInterface {

    @JsonValue
    String getValue();

    @JsonCreator
    static <E extends Enum<E> & EnumStringInterface> E getByValue(String value, Class<E> clazz) {
        for (E i : clazz.getEnumConstants()) {
            if (ParamUtil.strIsNotBlank(i.getValue()) && i.getValue().equals(value)) {
                return i;
            }
        }
        return null;
    }
}
