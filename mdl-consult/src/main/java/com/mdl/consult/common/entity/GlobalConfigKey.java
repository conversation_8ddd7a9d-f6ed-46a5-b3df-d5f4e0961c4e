package com.mdl.consult.common.entity;

/**
 * <AUTHOR>
 * @Description:  系统全局配置参数名称枚举类
 * @date 2020/12/19
 *
 */
public class GlobalConfigKey {
    /**
     * (public-server)
     */
    public static final String 系统_api_根Url = "sys.api.baseUrl";
    //系统配置
    public static final String 系统_api_前端地址 = "sys.api.frontEndHost";
    public static final String 系统_api_短链Url = "sys.api.shortUrl";
    public static final String 系统_文件_根url = "sys.file.baseUrl";
    public static final String 系统_OSS_根url = "sys.oss.baseUrl";

    /**
     * 系统_broker_业务服务IP(public-server)
     */
    public static final String 系统_broker_业务服务IP = "sys.broker.business.serverips";
    /**
     * 系统_broker_调试证书(public-server)
     */
    public static final String 系统_broker_调试证书 = "sys.broker.debugcert";

    public static final String 系统_测试_微信消息发送白名单 = "sys.test.sendmsg.whiteUserIds";
    public static final String 系统_测试_短信消息发送白名单 = "sys.test.sendmsg.whitePhoneNumber";

    //订单配置
    public static final String 订单_取消_超时自动取消_经销商 = "dealer.restock.cancel.overtime";
    public static final String 订单_取消_超时自动取消_盒子 = "member.box.order.unpaid.cancel.overtime";//扫码临时订单取消
    public static final String 订单_取消_超时自动取消_商城 = "member.mall.order.unpaid.cancel.overtime";
    public static final String 订单_自提_仓库 = "warehouse_id";

    /**
     * 商城订单发货超时提醒，超过N分钟提醒，单位（分）
     */
    public static final String store_order_delivery_overtime_remind = "store.order.delivery.overtime.remind";

    /**
     * 商城订单送达超时提醒，超过N分钟提醒，单位（分）
     */
    public static final String store_order_delivered_overtime_remind = "store.order.delivered.overtime.remind";

    /**
     * 经销商订单对接的仓库ID
     */
    public static final String dealer_restock_relatedWarehouse = "dealer.restock.relatedWarehouse";


    //地图配置
    /**
     * 地图第三方平台KEY
     */
    public static final String map_third_key = "map.third.key";

    //盒子证书配置
    /**
     * 盒子生产证书路径
     */
    public static final String box_ssl_certPath = "box.ssl.certPath";
    /**
     * 盒子根证书有效期
     */
    public static final String box_ssl_root_validity_period = "box.ssl.root.validity_period";
    /**
     * 盒子其他证书有效期
     */
    public static final String box_ssl_other_validity_period = "box.ssl.other.validity_period";
    /**
     * 盒子根证书ID
     */
    public static final String box_ssl_root_id = "box.ssl_root_id";
    /**
     * 双门盒子初始配置模版，deviceSn为设备序列号占位符
     */
    public static final String box_cab_config = "box.cab.config";
    /**
     * 亭式盒子初始配置模版，deviceSn为设备序列号占位符
     */
    public static final String box_big_config = "box.big.config";
    /**
     * 扫码超时N分钟拦截，-1不启用
     */
    public static final String box_order_intercept_overtime = "box.order.intercept.overtime";
    /**
     * 订单数量超过N支拦截，-1不启用
     */
    public static final String box_order_intercept_overnum = "box.order.intercept.overnum";

    //OSS配置
    public static final String 设备_证书_调试证书路径 = "device.ssl.certPath.debug";
    public static final String 设备_证书_根证书路径 = "device.ssl.certPath.rootCA";

    public static final String 设备_网络_检测IP = "device.network.detect.pingip";
    public static final String 设备_网络_检测间隔 = "device.system.status.detect.interval";
    public static final String 设备_红外_防抖间隔 = "device.ir.dither.damper.interval";
    public static final String 设备_红外_故障值 = "device.ir.dither.damper.timeout";
    public static final String 设备_红外_休眠计划 = "device.ir.power.controller.planning";
    public static final String 设备_红外_休眠 = "device.ir.power.controller.dormancy";
    public static final String 设备_红外_苏醒 = "device.ir.power.controller.revival";
    public static final String 设备_RFID_上报策略 = "device.rfid.read.strategy";
    public static final String 设备_状态_上报间隔 = "device.report.interval";
    public static final String 设备_门锁_亭式超时 = "device.close.door.timeout";
    public static final String 设备_门锁_柜式超时 = "device.cabinet.close.door.timeout";
    public static final String 设备_人感_超时 = "device.body.induction.timeout";
    public static final String 设备_库存_校对_亭式 = "device.box.delay.checking.after.departure";
    public static final String 设备_库存_校对_柜式 = "device.cabinet.delay.checking.after.departure";
    public static final String 设备_文件_上传地址 = "device.file.upload.url";
    /**
     * OSS对外服务访问地址
     */
    public static final String aliOSS_endpoint = "aliOSS.endpoint";

    public static final String aliOSS_bucket_public_endpoint = "aliOSS.bucket.public.endpoint";
    /**
     * 提醒补货员完成补货的公众号推送时间间隔（秒）
     */
    public static final String REPLENISHMAN_REMIND_PERIOD = "replenishman_remind_period";

    /**
     * 会员福利费利率 单位：%
     */
    public static final String MEMBER_BENEFITS_RATE = "member_benefits_rate";

    /**
     * 用户意向单等待时间取消的时间间隔 单位为秒
     */
    public static final String RESTAURANT_INTENTIONAL_ORDER_CANCEL_TIME = "restaurant_intentional_order_cancel_time";

    /**
     * 业务员提成比率 (商户结算时用作计算业务员提成用，该功能已弃用,数据库没有数据)
     */
    @Deprecated
    public static final String DEALER_ROYALTY_RATE = "dealer_royalty_rate";

    /**
     * 代购员线上下单功能是否可用
     */
    public static final String ONLINE_ORDER_AVAILABLE = "online_order_available";

    /**
     * 线上订单过期时间  单位:秒
     */
    public static final String ONLINE_ORDER_EXPIRES_TIME = "online_order_expires_time";
    /**
     * OSS_KEY
     */
    public static final String aliOSS_bucket_public_key = "aliOSS.bucket.public.key";
    /**
     * OSS_SECRET
     */
    public static final String aliOSS_bucket_public_secret = "aliOSS.bucket.public.secret";
    /**
     * OSS存储空间
     */
    public static final String aliOSS_bucket_public = "aliOSS.bucket.public";
    /**
     * OSS_CNAME
     */
    public static final String aliOSS_bucket_public_cname = "aliOSS.bucket.public.cname";
    /**
     * 私有空间 OSS_KEY
     */
    public static final String aliOSS_bucket_private_key = "aliOSS.bucket.private.key";
    /**
     * 私有空间 OSS_SECRET
     */
    public static final String aliOSS_bucket_private_secret = "aliOSS.bucket.private.secret";
    /**
     * 私有空间 OSS存储空间
     */
    public static final String aliOSS_bucket_private = "aliOSS.bucket.private";
    /**
     * 私有空间 OSS_CNAME
     */
    public static final String aliOSS_bucket_private_cname = "aliOSS.bucket.private.cname";
    /**
     * 扫码点餐购物车有效期,单位：秒
     */
    public static final String qrcode_order_effectivetime_car = "qrcode.order.effectivetime.car";
    /**
     * 经销商提现规则描述
     */
    public static final String dealer_income_withdraw_describe = "dealer.income.withdraw.describe";
    /**
     * 经销商单次提现限额,单位：分
     */
    public static final String dealer_income_withdraw_limited = "dealer.income.withdraw.limited";
    /**
     * 盒子监控抓拍图片 OSS_KEY
     */
    public static final String aliOSS_bucket_snap_key = "aliOSS.bucket.snap.key";
    /**
     * 盒子监控抓拍图片 OSS_SECRET
     */
    public static final String aliOSS_bucket_snap_secret = "aliOSS.bucket.snap.secret";
    /**
     * 盒子监控抓拍图片 名称
     */
    public static final String aliOSS_bucket_snap = "aliOSS.bucket.snap";
    /**
     * 盒子监控抓拍图片 对外服务地址
     */
    public static final String aliOSS_bucket_snap_endpoint = "aliOSS.bucket.snap.endpoint";


    /**
     * 是否虚拟支付0：否，1：是
     */
    public static final String pay_fakePayment = "pay.fakePayment";

    /**
     * 阿里免密订单最大上限金额,单位：分(public-server)
     */
    public static final String pay_ali_contract_maxamount = "pay.ali.contract.maxamount";

    /**
     * 微信协议签约服务id(public-server)
     */
    public static final String pay_wx_contract_serviceid = "pay.wx.contract.serviceid";

    /**
     * 微信免密订单最大上限金额,单位：分(public-server)
     */
    public static final String pay_wx_contract_maxamount = "pay.wx.contract.maxamount";


    /**
     * 用于设置空调温度预警值
     */
    public static final String air_temperature_warn = "air.temperature.warn";
    /**
     * 设备的红外电压数值区间配置
     */
    public static final String device_infrared_voltage_range_setting = "device.infrared.voltage.range.setting";

    /**
     * 物流立即配送重量单位(可选：kg、g)参数
     */
    public static final String 物流_微信_重量单位 = "delivery.freight.weight.unit.param";
    /**
     * 物流立即配送重量(单位根据重量单位配置)参数
     */
    public static final String 物流_单个商品重量 = "delivery.freight.weight.param";
    /**
     * 物流立即配送取消原因ID参数
     */
    public static final String 物流_微信_订单取消原因ID = "delivery.cancel.reasonId.param";
    /**
     * 消息通知详情跳转路径
     */
    public static final String 物流_微信_物流通知详情路径 = "delivery.wxa.path.param";
    /**
     * 物流详情商品详情路径
     */
    public static final String 物流_微信_物流详情_商品详情路径 = "delivery.orderDetail.path.param";
    /**
     * 物流立即配送appkey参数
     */
    public static final String 物流_微信_闪送_APPKEY = "delivery.weixin.shansong.appkey";
    public static final String 物流_微信_闪送_APPSECRET = "delivery.weixin.shansong.appsecret";
    public static final String 物流_微信_达达_APPKEY = "delivery.shopid.param";
    public static final String 物流_微信_达达_APPSECRET = "delivery.appsecret.param";
    public static final String 物流_配送_费用上限 = "delivery.limit.amount.param";
    public static final String 物流_测试 = "delivery.test";
    public static final String 物流_微信_测试_物流公司ID = "delivery.test.deliveryId";
    public static final String 物流_微信_测试_APPKEY = "delivery.test.shopid.param";
    public static final String 物流_微信_测试_APPSECRET = "delivery.test.appsecret.param";
    /**
     * 沙盒测试物流运费(单位：分)参数
     */
    public static final String 物流_微信_测试_运费 = "delivery.test.ship.fee";
    /**
     * 经销商服务距离范围限制数值
     */
    public static final String 物流_最大配送范围 = "delivery.dealer.service.distance.range";

    /**
     * 会员订单免邮额(分)
     */
    public static final String 物流_配送_会员_订单免邮额 = "delivery.member.shipfee.freeamount";

    /**
     * 订货会活动开始前N小时通知数值
     */
    public static final String subscribe_schedule_hour_time_value = "subscribe.schedule.hour.time.value";

    /**
     * 微信小程序码跳转的小程序版本
     */
    public static final String sys_wx_qrcode_envversion = "sys.wx.qrcode.envversion";

    /**
     * 订单可以退款间隔时间/秒
     */
    public static final String order_refund_able_interval_time = "order.refund.able.interval.time";

    /**
     * 订单结算间隔时间/秒
     */
    public static final String 会员_订单_结算时间 = "order.settlement.interval.time";
    public static final String 会员_订单_结算时间_分享佣金 = "morder.settle.time.sharecommission";

   /**
     * mqtthttp请求url(device-agent)
     */
   public static final String mqtt_broker_http_url = "mqtt.broker.http.url";

    /**
     * mqtthttp请求用户名(device-agent)
     */
    public static final String mqtt_broker_http_user_name = "mqtt.broker.http.user.name";

    /**
     * mqtthttp请求密码(device-agent)
     */
    public static final String mqtt_broker_http_password = "mqtt.broker.http.password";

    /**
     * 盒子自用商品平台收的服务费,百分比
     */
    public static final String fee_service_box_self_use_goods = "fee.service.box.self.use.goods";


    public static final String 微信_公众号_APPID = "weixin.official.appid";
    public static final String 微信_公众号_APPSECRET = "weixin.official.appsecret";
    public static final String 微信_公众号_TOKEN = "weixin.official.token";
    public static final String 微信_公众号_AESKEY = "weixin.official.aeskey";

    public static final String 微信_小程序_维修_APPID = "weixin.minipro.maintain.appid";
    public static final String 微信_小程序_维修_APPSECRET = "weixin.minipro.maintain.appsecret";

    public static final String 微信_小程序_代购_APPID = "weixin.minipro.agent.appid";
    public static final String 微信_小程序_代购_APPSECRET = "weixin.minipro.agent.appsecret";

    public static final String 微信_小程序_经销商_APPID = "weixin.minipro.dealer.appid";
    public static final String 微信_小程序_经销商_APPSECRET = "weixin.minipro.dealer.appsecret";

    public static final String 微信_小程序_补货_APPID = "weixin.minipro.replenish.appid";
    public static final String 微信_小程序_补货_APPSECRET = "weixin.minipro.replenish.appsecret";

    public static final String 微信_小程序_商城_APPID = "weixin.minipro.mall.appid";
    public static final String 微信_小程序_商城_APPSECRET = "weixin.minipro.mall.appsecret";

    public static final String 微信_小程序_会员_APPID = "weixin.minipro.member.appid";
    public static final String 微信_小程序_会员_APPSECRET = "weixin.minipro.member.appsecret";

    public static final String 微信_小程序_门店_APPID = "weixin.minipro.store.appid";
    public static final String 微信_小程序_门店_APPSECRET = "weixin.minipro.store.appsecret";
    /**
     * 会员收益提现限额,单位：分(public-server)
     */
    public static final String member_income_withdraw_limited = "menber.income.withdraw.limited";

    /**
     * 摩萄商城id
     */
    public static final String motao_mall_id = "motao.mall.id";

    /**
     * 会员专属活动id
     */
    public static final String member_exclusive_activity_id = "member.exclusive.activity.id";

    /**
     * 会员新品活动id
     */
    public static final String member_new_product_activity_id = "member.new.product.activity.id";

    /**
     * 线下购买电话
     */
    public static final String 经销商_线下购买_电话 = "dealer.offline.purchase.phone";
    public static final String 会员_默认归属盒子 = "member.belongbox";
    public static final String 会员_默认批发商城 = "member.wholesalebox";
    public static final String 会员_活动_积分_有效期 = "member.promotion.points.validityPeriod";
    public static final String 阿里云_积分变动短信模板 = "aliSms.pointsChangeTemplate";
    public static final String 会员商城_盒子_分享商城ID = "mmall.box.shareMallId";
    public static final String 支付_收款商家_默认 = "pay.collection.dealer.default";
    public static final String 支付_转款商家_默认 = "pay.transfer.dealer.default";

}
