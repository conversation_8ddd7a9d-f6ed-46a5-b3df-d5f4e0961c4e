package com.mdl.consult.common.tools;

import com.mdl.consult.common.exception.ApiException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通用参数验证工具类
 */
@Component
public class ValidatorUtil {
    private static Validator validator;

    public ValidatorUtil(Validator validator) {
        ValidatorUtil.validator = validator;
    }

    /**
     * 通用参数验证方法
     * @param param 需要验证的参数对象
     * @param paramName 参数名称
     * @param <T> 参数类型
     */
    public static <T> void validateParam(T param, String paramName) {
        ParamUtil.checkObjParam(param, paramName);
        Set<ConstraintViolation<T>> violations = validator.validate(param);
        if (!violations.isEmpty()) {
            String errorMsg = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", "));
            throw new ApiException(errorMsg);
        }
    }
} 