package com.mdl.consult.common.tools;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 图片水印工具类
 * 支持添加文字水印到图片，支持多种位置和样式设置
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
public class ImageWatermarkUtil {

    /**
     * 水印位置枚举
     */
    public enum WatermarkPosition {
        TOP_LEFT,    // 左上
        TOP_RIGHT,   // 右上
        BOTTOM_LEFT, // 左下
        BOTTOM_RIGHT,// 右下
        CENTER       // 居中
    }

    /**
     * 支持的图片格式
     */
    private static final Set<String> SUPPORTED_FORMATS = new HashSet<>(Arrays.asList("jpg", "jpeg", "png", "bmp", "gif"));

    /**
     * 默认字体
     */
    private static final String DEFAULT_FONT = "微软雅黑";

    /**
     * 默认字体大小
     */
    private static final int DEFAULT_FONT_SIZE = 30;

    /**
     * 默认透明度
     */
    private static final float DEFAULT_OPACITY = 0.3f;

    /**
     * 默认水印颜色
     */
    private static final Color DEFAULT_COLOR = Color.BLACK;

    /**
     * 添加水印到文件
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param text       水印文字
     * @param color      水印颜色
     * @param position   水印位置
     */
    public static boolean addWatermark(String inputPath, String outputPath, String text, Color color, WatermarkPosition position) {
        try {
            // 参数验证
            ParamUtil.checkStrParam(inputPath, "inputPath");
            ParamUtil.checkStrParam(outputPath, "outputPath");
            ParamUtil.checkStrParam(text, "text");
            ParamUtil.checkObjParam(color, "color");
            ParamUtil.checkObjParam(position, "position");

            // 验证文件格式
            String format = getImageFormat(inputPath);
            if (!SUPPORTED_FORMATS.contains(format.toLowerCase())) {
                throw new IllegalArgumentException("不支持的图片格式: " + format);
            }

            // 读取图片
            BufferedImage image = ImageIO.read(new File(inputPath));
            if (image == null) {
                throw new IllegalArgumentException("无法读取图片: " + inputPath);
            }

            // 添加水印
            BufferedImage watermarkedImage = addWatermark(image, text, color, position);

            // 保存图片
            ImageIO.write(watermarkedImage, format, new File(outputPath));
            return true;
        } catch (Exception e) {
            log.error("添加水印失败. inputPath:{}, outputPath:{}, text:{}, color:{}", inputPath, outputPath, text, color, position,e);
        }

        return false;
    }

    /**
     * 添加水印到输入流
     *
     * @param inputStream 输入流
     * @param text        水印文字
     * @param color       水印颜色
     * @param position    水印位置
     * @return 带有水印的图片输入流
     */
    public static InputStream addWatermark(InputStream inputStream, String text, Color color, WatermarkPosition position) {
        try {
            // 参数验证
            ParamUtil.checkObjParam(inputStream, "inputStream");
            ParamUtil.checkStrParam(text, "text");
            ParamUtil.checkObjParam(color, "color");
            ParamUtil.checkObjParam(position, "position");

            // 读取图片
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                throw new IllegalArgumentException("无法读取图片流");
            }

            // 添加水印
            BufferedImage watermarkedImage = addWatermark(image, text, color, position);

            // 转换为输入流
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(watermarkedImage, "png", os);
            return new ByteArrayInputStream(os.toByteArray());
        } catch (Exception e) {
            log.error("添加水印失败", e);
            throw new RuntimeException("添加水印失败: " + e.getMessage());
        }
    }

    /**
     * 添加水印到BufferedImage
     *
     * @param image    原始图片
     * @param text     水印文字
     * @param color    水印颜色
     * @param position 水印位置
     * @return 带有水印的图片
     */
    public static BufferedImage addWatermark(BufferedImage image, String text, Color color, WatermarkPosition position) {
        // 创建图片副本
        BufferedImage watermarkedImage = new BufferedImage(
                image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = watermarkedImage.createGraphics();

        // 设置图片质量
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制原始图片
        g2d.drawImage(image, 0, 0, null);

        // 设置水印样式
        g2d.setFont(new Font(DEFAULT_FONT, Font.PLAIN, DEFAULT_FONT_SIZE));
        g2d.setColor(color);
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));

        // 计算多行文本的总宽度和高度
        FontMetrics fontMetrics = g2d.getFontMetrics();
        String[] lines = text.split("\n");
        int maxWidth = 0;
        int totalHeight = 0;
        for (String line : lines) {
            int lineWidth = fontMetrics.stringWidth(line);
            maxWidth = Math.max(maxWidth, lineWidth);
            totalHeight += fontMetrics.getHeight();
        }
        int ascent = fontMetrics.getAscent();

        // 计算水印位置
        Point watermarkPoint = calculateWatermarkPosition(image.getWidth(), image.getHeight(), maxWidth, totalHeight, position, ascent);

        // 绘制多行水印
        int y = watermarkPoint.y;
        for (String line : lines) {
            g2d.drawString(line, watermarkPoint.x, y);
            y += fontMetrics.getHeight();
        }
        g2d.dispose();
        return watermarkedImage;
    }

    /**
     * 计算水印位置
     * @param ascent 仅顶部水印时用于首行基线对齐顶部
     */
    private static Point calculateWatermarkPosition(int imageWidth, int imageHeight, int textWidth, int textHeight, WatermarkPosition position, int ascent) {
        int x, y;
        int padding = 10; // 默认边距
        switch (position) {
            case TOP_LEFT:
                x = padding;
                // 采用ascent让首行基线紧贴顶部
                y = padding + ascent;
                break;
            case TOP_RIGHT:
                x = imageWidth - textWidth - padding;
                y = padding + ascent;
                break;
            case BOTTOM_LEFT:
                x = padding;
                y = imageHeight - padding;
                break;
            case BOTTOM_RIGHT:
                x = imageWidth - textWidth - padding;
                y = imageHeight - padding;
                break;
            case CENTER:
                x = (imageWidth - textWidth) / 2;
                y = (imageHeight + textHeight) / 2;
                break;
            default:
                x = padding;
                y = padding + ascent;
        }
        return new Point(x, y);
    }

    /**
     * 获取图片格式
     */
    private static String getImageFormat(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex == -1) {
            throw new IllegalArgumentException("文件格式无法识别");
        }
        return filePath.substring(lastDotIndex + 1);
    }

    public static void main(String[] args) throws Exception {
        String outputPath = "/Users/<USER>/Downloads/text.jpg";
        String inputPath = "/Users/<USER>/Downloads/9.jpg";
        // 测试添加水印
        ImageWatermarkUtil.addWatermark(
                inputPath,
                outputPath,
                "救援师傅：吴xx，1393988444\n" +
                        "救援时间：2025-05-02 01:02:32\n" +
                        "地址定位：12332.32,3434.33",
                Color.RED,
                WatermarkPosition.TOP_LEFT
        );
    }
} 