package com.mdl.consult.common.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mdl.consult.common.tools.JsonUtil;
import lombok.Data;
import org.springframework.ui.Model;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> implements Serializable {
    private int code;
    private String message;
    private T data;

    public ApiResponse() {
        this.code = 200;
        this.message = "成功";
    }

    public ApiResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "成功", data);
    }

    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "成功", null);
    }

    public static <T> ApiResponse<T> failure(int code, String message) {
        return new ApiResponse<>(code, message);
    }

    public static <T> void success(T data, Model model) {
        ApiResponse apiResponse = success(data);
        model.addAttribute("code",apiResponse.getCode());
        model.addAttribute("message",apiResponse.getMessage());
        model.addAttribute("data",apiResponse.getData());
    }

    @Override
    public String toString(){
        return JsonUtil.beanToString(this);
    }
}
