package com.mdl.consult.common.tools;/**
 * <AUTHOR>
 * @Date 2025/1/22
 */

/**
 * 描述
 * @className BooleanUtil
 * <AUTHOR>
 * @date 2025年01月22日 08:28
 */
public class BooleanUtil{
    public static boolean toBoolean(Integer value) {
        return value == null ? false : value > 0;
    }

    public static Integer toInteger(boolean value) {
        return value ? 1 : 0;
    }

    public static Integer toInt(Boolean value) {
        return value == null ? 0 : (value ? 1 : 0);
    }
}
