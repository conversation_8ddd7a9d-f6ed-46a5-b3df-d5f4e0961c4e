package com.mdl.consult.common.exception;

import lombok.Data;

/**
 * <AUTHOR>  2020/2/28
 */
@Data
public class LockException extends RuntimeException {

    /**无法获取锁*/
    public static final int GET_LOCK_FAIL = 4001;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String message;

    public LockException(String message, int code) {
        super(message);
        this.code = code;
        this.message = message;
    }
}
