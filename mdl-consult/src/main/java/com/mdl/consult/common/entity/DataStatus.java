package com.mdl.consult.common.entity;/**
 * <AUTHOR>
 * @Date 2025/1/21
 */

/**
 * 描述
 * @className TechCopStatus
 * <AUTHOR>
 * @date 2025年01月21日 15:34
 */
public enum DataStatus implements EnumIntegerInterface {
    待审(-1),
    启用(1),
    禁用(0);
    private Integer value;

    DataStatus(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static DataStatus getByValue(Integer value) {
        return EnumIntegerInterface.getByValue(value, DataStatus.class);
    }
}
