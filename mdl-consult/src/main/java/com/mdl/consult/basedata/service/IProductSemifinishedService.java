package com.mdl.consult.basedata.service;

import java.util.List;
import com.mdl.consult.basedata.domain.ProductSemifinished;

/**
 * 产成品对应的半成品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface IProductSemifinishedService
{
    /**
     * 查询产成品对应的半成品
     *
     * @param id 产成品对应的半成品主键
     * @return 产成品对应的半成品
     */
    public ProductSemifinished selectProductSemifinishedById(Long id);

    /**
     * 查询产成品对应的半成品列表
     *
     * @param productSemifinished 产成品对应的半成品
     * @return 产成品对应的半成品集合
     */
    public List<ProductSemifinished> selectProductSemifinishedList(ProductSemifinished productSemifinished);

    /**
     * 新增产成品对应的半成品
     *
     * @param productSemifinished 产成品对应的半成品
     * @return 结果
     */
    public int insertProductSemifinished(ProductSemifinished productSemifinished);

    /**
     * 修改产成品对应的半成品
     *
     * @param productSemifinished 产成品对应的半成品
     * @return 结果
     */
    public int updateProductSemifinished(ProductSemifinished productSemifinished);

    /**
     * 批量删除产成品对应的半成品
     *
     * @param ids 需要删除的产成品对应的半成品主键集合
     * @return 结果
     */
    public int deleteProductSemifinishedByIds(String ids);

    /**
     * 删除产成品对应的半成品信息
     *
     * @param id 产成品对应的半成品主键
     * @return 结果
     */
    public int deleteProductSemifinishedById(Long id);

    /**
     * 根据数据分析ID删除产成品对应的半成品数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteProductSemifinishedByDataAnalysisId(Long dataAnalysisId);

    void calData(ProductSemifinished semifinished);
}
