package com.mdl.consult.basedata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.basedata.mapper.BomMapper;
import com.mdl.consult.basedata.domain.Bom;
import com.mdl.consult.basedata.service.IBomService;
import com.ruoyi.common.core.text.Convert;

/**
 * BOMService业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class BomServiceImpl implements IBomService
{
    @Autowired
    private BomMapper bomMapper;

    /**
     * 查询BOM
     *
     * @param id BOM主键
     * @return BOM
     */
    @Override
    public Bom selectBomById(Long id)
    {
        return bomMapper.selectBomById(id);
    }

    /**
     * 查询BOM列表
     *
     * @param bom BOM
     * @return BOM
     */
    @Override
    public List<Bom> selectBomList(Bom bom)
    {
        return bomMapper.selectBomList(bom);
    }

    /**
     * 新增BOM
     *
     * @param bom BOM
     * @return 结果
     */
    @Override
    public int insertBom(Bom bom)
    {
        bom.setCreateTime(DateUtils.getNowDate());
        return bomMapper.insertBom(bom);
    }

    /**
     * 修改BOM
     *
     * @param bom BOM
     * @return 结果
     */
    @Override
    public int updateBom(Bom bom)
    {
        bom.setUpdateTime(DateUtils.getNowDate());
        return bomMapper.updateBom(bom);
    }

    /**
     * 批量删除BOM
     *
     * @param ids 需要删除的BOM主键
     * @return 结果
     */
    @Override
    public int deleteBomByIds(String ids)
    {
        return bomMapper.deleteBomByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除BOM信息
     *
     * @param id BOM主键
     * @return 结果
     */
    @Override
    public int deleteBomById(Long id)
    {
        return bomMapper.deleteBomById(id);
    }

    /**
     * 根据数据分析ID删除BOM数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteBomByDataAnalysisId(Long dataAnalysisId)
    {
        return bomMapper.deleteBomByDataAnalysisId(dataAnalysisId);
    }
}
