package com.mdl.consult.basedata.mapper;

import java.util.List;
import com.mdl.consult.basedata.domain.SafetyStockFinishedProduct;

/**
 * 产成品安全库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface SafetyStockFinishedProductMapper
{
    /**
     * 查询产成品安全库存
     *
     * @param id 产成品安全库存主键
     * @return 产成品安全库存
     */
    public SafetyStockFinishedProduct selectSafetyStockFinishedProductById(Long id);

    /**
     * 查询产成品安全库存列表
     *
     * @param safetyStockFinishedProduct 产成品安全库存
     * @return 产成品安全库存集合
     */
    public List<SafetyStockFinishedProduct> selectSafetyStockFinishedProductList(SafetyStockFinishedProduct safetyStockFinishedProduct);

    /**
     * 新增产成品安全库存
     *
     * @param safetyStockFinishedProduct 产成品安全库存
     * @return 结果
     */
    public int insertSafetyStockFinishedProduct(SafetyStockFinishedProduct safetyStockFinishedProduct);

    /**
     * 修改产成品安全库存
     *
     * @param safetyStockFinishedProduct 产成品安全库存
     * @return 结果
     */
    public int updateSafetyStockFinishedProduct(SafetyStockFinishedProduct safetyStockFinishedProduct);

    /**
     * 删除产成品安全库存
     *
     * @param id 产成品安全库存主键
     * @return 结果
     */
    public int deleteSafetyStockFinishedProductById(Long id);

    /**
     * 批量删除产成品安全库存
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSafetyStockFinishedProductByIds(String[] ids);

    /**
     * 根据数据分析ID删除产成品安全库存数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteSafetyStockFinishedProductByDataAnalysisId(Long dataAnalysisId);
}
