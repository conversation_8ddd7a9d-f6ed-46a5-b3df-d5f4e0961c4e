package com.mdl.consult.basedata.controller;

import java.util.List;

import com.mdl.consult.analysis.mapper.DataSumMapper;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.basedata.domain.BomPackaging;
import com.mdl.consult.basedata.service.IBomPackagingService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 包装BOMController
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/basedata/bom-package")
public class BomPackagingController extends BaseController
{
    @Autowired
    private DataSumMapper materialShortageAnalysisMapper;
    private String prefix = "consult/basedata/bom-package";

    @Autowired
    private IBomPackagingService bomPackagingService;

    @RequiresPermissions("consult/basedata:bom-package:view")
    @GetMapping()
    public String bomPackage()
    {
        return prefix + "/bom-package";
    }

    /**
     * 查询包装BOM列表
     */
    @RequiresPermissions("consult/basedata:bom-package:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BomPackaging bomPackaging)
    {
        startPage();
        List<BomPackaging> list = bomPackagingService.selectBomPackagingList(bomPackaging);
        return getDataTable(list);
    }

    /**
     * 导出包装BOM列表
     */
    @RequiresPermissions("consult/basedata:bom-package:export")
    @Log(title = "包装BOM", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BomPackaging bomPackaging)
    {
        List<BomPackaging> list = bomPackagingService.selectBomPackagingList(bomPackaging);
        ExcelUtil<BomPackaging> util = new ExcelUtil<BomPackaging>(BomPackaging.class);
        return util.exportExcel(list, "包装BOM数据");
    }

    /**
     * 新增包装BOM
     */
    @RequiresPermissions("consult/basedata:bom-package:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存包装BOM
     */
    @RequiresPermissions("consult/basedata:bom-package:add")
    @Log(title = "包装BOM", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BomPackaging bomPackaging)
    {
        return toAjax(bomPackagingService.insertBomPackaging(bomPackaging));
    }

    /**
     * 修改包装BOM
     */
    @RequiresPermissions("consult/basedata:bom-package:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        BomPackaging bomPackaging = bomPackagingService.selectBomPackagingById(id);
        mmap.put("bomPackaging", bomPackaging);
        return prefix + "/edit";
    }

    /**
     * 修改保存包装BOM
     */
    @RequiresPermissions("consult/basedata:bom-package:edit")
    @Log(title = "包装BOM", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BomPackaging bomPackaging)
    {
        return toAjax(bomPackagingService.updateBomPackaging(bomPackaging));
    }

    /**
     * 删除包装BOM
     */
    @RequiresPermissions("consult/basedata:bom-package:remove")
    @Log(title = "包装BOM", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bomPackagingService.deleteBomPackagingByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/basedata:bom-package:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<BomPackaging> util = new ExcelUtil<BomPackaging>(BomPackaging.class);
        util.hideColumn("orderQuantity","materialRequirementSubtotal");
        return util.importTemplateExcel("包装BOM数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/basedata:bom-package:import")
    @Log(title = "包装BOM", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<BomPackaging> util = new ExcelUtil<BomPackaging>(BomPackaging.class);
        List<BomPackaging> bomPackagingList = util.importExcel(file.getInputStream());
        String message = importBomPackaging(dataAnalysisId,bomPackagingList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入包装BOM数据
     *
     * @param dataAnalysisId
     * @param bomPackagingList 包装BOM数据列表
     * @param isUpdateSupport  是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importBomPackaging(Long dataAnalysisId, List<BomPackaging> bomPackagingList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(bomPackagingList) || bomPackagingList.size() == 0)
        {
            throw new ServiceException("导入包装BOM数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BomPackaging bomPackaging : bomPackagingList)
        {
            try
            {
                if(StringUtils.isBlank(bomPackaging.getChildCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、子件编码不能为空");
                }else {
                    bomPackaging.setDataAnalysisId(dataAnalysisId);
                    bomPackagingService.insertBomPackaging(bomPackaging);
                }
                successNum++;
                successMsg.append("<br/>" + successNum + "、包装BOM " + bomPackaging.getParentName() + " 导入成功");
                /* 验证是否存在这个包装BOM
                BomPackaging b = bomPackagingService.selectBomPackagingById(bomPackaging.getId());
                if (StringUtils.isNull(b))
                {
                    bomPackagingService.insertBomPackaging(bomPackaging);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、包装BOM " + bomPackaging.getParentName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    bomPackagingService.updateBomPackaging(bomPackaging);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、包装BOM " + bomPackaging.getParentName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、包装BOM " + bomPackaging.getParentName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、包装BOM " + bomPackaging.getParentName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除包装BOM数据
     */
    @RequiresPermissions("consult/basedata:bom-package:remove")
    @Log(title = "包装BOM", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(bomPackagingService.deleteBomPackagingByDataAnalysisId(dataAnalysisId));
    }
}
