package com.mdl.consult.basedata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.basedata.mapper.SafetyStockMaterialMapper;
import com.mdl.consult.basedata.domain.SafetyStockMaterial;
import com.mdl.consult.basedata.service.ISafetyStockMaterialService;
import com.ruoyi.common.core.text.Convert;

/**
 * 物料安全库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class SafetyStockMaterialServiceImpl implements ISafetyStockMaterialService
{
    @Autowired
    private SafetyStockMaterialMapper safetyStockMaterialMapper;

    /**
     * 查询物料安全库存
     *
     * @param id 物料安全库存主键
     * @return 物料安全库存
     */
    @Override
    public SafetyStockMaterial selectSafetyStockMaterialById(Long id)
    {
        return safetyStockMaterialMapper.selectSafetyStockMaterialById(id);
    }

    /**
     * 查询物料安全库存列表
     *
     * @param safetyStockMaterial 物料安全库存
     * @return 物料安全库存
     */
    @Override
    public List<SafetyStockMaterial> selectSafetyStockMaterialList(SafetyStockMaterial safetyStockMaterial)
    {
        return safetyStockMaterialMapper.selectSafetyStockMaterialList(safetyStockMaterial);
    }

    /**
     * 新增物料安全库存
     *
     * @param safetyStockMaterial 物料安全库存
     * @return 结果
     */
    @Override
    public int insertSafetyStockMaterial(SafetyStockMaterial safetyStockMaterial)
    {
        safetyStockMaterial.setCreateTime(DateUtils.getNowDate());
        return safetyStockMaterialMapper.insertSafetyStockMaterial(safetyStockMaterial);
    }

    /**
     * 修改物料安全库存
     *
     * @param safetyStockMaterial 物料安全库存
     * @return 结果
     */
    @Override
    public int updateSafetyStockMaterial(SafetyStockMaterial safetyStockMaterial)
    {
        safetyStockMaterial.setUpdateTime(DateUtils.getNowDate());
        return safetyStockMaterialMapper.updateSafetyStockMaterial(safetyStockMaterial);
    }

    /**
     * 批量删除物料安全库存
     *
     * @param ids 需要删除的物料安全库存主键
     * @return 结果
     */
    @Override
    public int deleteSafetyStockMaterialByIds(String ids)
    {
        return safetyStockMaterialMapper.deleteSafetyStockMaterialByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除物料安全库存信息
     *
     * @param id 物料安全库存主键
     * @return 结果
     */
    @Override
    public int deleteSafetyStockMaterialById(Long id)
    {
        return safetyStockMaterialMapper.deleteSafetyStockMaterialById(id);
    }

    /**
     * 根据数据分析ID删除物料安全库存数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteSafetyStockMaterialByDataAnalysisId(Long dataAnalysisId)
    {
        return safetyStockMaterialMapper.deleteSafetyStockMaterialByDataAnalysisId(dataAnalysisId);
    }
}
