package com.mdl.consult.basedata.mapper;

import java.util.List;
import com.mdl.consult.basedata.domain.Bom;

/**
 * BOMMapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface BomMapper
{
    /**
     * 查询BOM
     *
     * @param id BOM主键
     * @return BOM
     */
    public Bom selectBomById(Long id);

    /**
     * 查询BOM列表
     *
     * @param bom BOM
     * @return BOM集合
     */
    public List<Bom> selectBomList(Bom bom);

    /**
     * 新增BOM
     *
     * @param bom BOM
     * @return 结果
     */
    public int insertBom(Bom bom);

    /**
     * 修改BOM
     *
     * @param bom BOM
     * @return 结果
     */
    public int updateBom(Bom bom);

    /**
     * 删除BOM
     *
     * @param id BOM主键
     * @return 结果
     */
    public int deleteBomById(Long id);

    /**
     * 批量删除BOM
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBomByIds(String[] ids);

    /**
     * 根据数据分析ID删除BOM数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteBomByDataAnalysisId(Long dataAnalysisId);
}
