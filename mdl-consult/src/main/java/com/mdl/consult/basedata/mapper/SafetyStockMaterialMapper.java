package com.mdl.consult.basedata.mapper;

import java.util.List;
import com.mdl.consult.basedata.domain.SafetyStockMaterial;

/**
 * 物料安全库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface SafetyStockMaterialMapper
{
    /**
     * 查询物料安全库存
     *
     * @param id 物料安全库存主键
     * @return 物料安全库存
     */
    public SafetyStockMaterial selectSafetyStockMaterialById(Long id);

    /**
     * 查询物料安全库存列表
     *
     * @param safetyStockMaterial 物料安全库存
     * @return 物料安全库存集合
     */
    public List<SafetyStockMaterial> selectSafetyStockMaterialList(SafetyStockMaterial safetyStockMaterial);

    /**
     * 新增物料安全库存
     *
     * @param safetyStockMaterial 物料安全库存
     * @return 结果
     */
    public int insertSafetyStockMaterial(SafetyStockMaterial safetyStockMaterial);

    /**
     * 修改物料安全库存
     *
     * @param safetyStockMaterial 物料安全库存
     * @return 结果
     */
    public int updateSafetyStockMaterial(SafetyStockMaterial safetyStockMaterial);

    /**
     * 删除物料安全库存
     *
     * @param id 物料安全库存主键
     * @return 结果
     */
    public int deleteSafetyStockMaterialById(Long id);

    /**
     * 批量删除物料安全库存
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSafetyStockMaterialByIds(String[] ids);

    /**
     * 根据数据分析ID删除物料安全库存数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteSafetyStockMaterialByDataAnalysisId(Long dataAnalysisId);
}
