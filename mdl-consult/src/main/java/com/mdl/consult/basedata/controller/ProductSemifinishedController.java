package com.mdl.consult.basedata.controller;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mdl.consult.analysis.mapper.DataSumMapper;
import com.mdl.consult.basedata.service.impl.ProductSemifinishedServiceImpl;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.basedata.domain.ProductSemifinished;
import com.mdl.consult.basedata.service.IProductSemifinishedService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 产成品对应的半成品Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/basedata/product-semifinished")
public class ProductSemifinishedController extends BaseController {
    private String prefix = "consult/basedata/product-semifinished";

    @Autowired
    private ProductSemifinishedServiceImpl productSemifinishedService;

    @RequiresPermissions("consult/basedata:product-semifinished:view")
    @GetMapping()
    public String productSemifinished()
    {
        return prefix + "/product-semifinished";
    }

    /**
     * 查询产成品对应的半成品列表
     */
    @RequiresPermissions("consult/basedata:product-semifinished:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductSemifinished productSemifinished)
    {
        startPage();
        List<ProductSemifinished> list = productSemifinishedService.list(new LambdaQueryWrapper<>(productSemifinished));
        return getDataTable(list);
    }

    /**
     * 导出产成品对应的半成品列表
     */
    @RequiresPermissions("consult/basedata:product-semifinished:export")
    @Log(title = "产成品对应的半成品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProductSemifinished productSemifinished)
    {
        List<ProductSemifinished> list = productSemifinishedService.list(new LambdaQueryWrapper<>(productSemifinished));
        ExcelUtil<ProductSemifinished> util = new ExcelUtil<ProductSemifinished>(ProductSemifinished.class);
        return util.exportExcel(list, "产成品对应的半成品数据");
    }

    /**
     * 新增产成品对应的半成品
     */
    @RequiresPermissions("consult/basedata:product-semifinished:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存产成品对应的半成品
     */
    @RequiresPermissions("consult/basedata:product-semifinished:add")
    @Log(title = "产成品对应的半成品", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProductSemifinished productSemifinished) {
        return toAjax(productSemifinishedService.insertProductSemifinished(productSemifinished));
    }

    /**
     * 修改产成品对应的半成品
     */
    @RequiresPermissions("consult/basedata:product-semifinished:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        ProductSemifinished productSemifinished = productSemifinishedService.selectProductSemifinishedById(id);
        mmap.put("productSemifinished", productSemifinished);
        return prefix + "/edit";
    }

    /**
     * 修改保存产成品对应的半成品
     */
    @RequiresPermissions("consult/basedata:product-semifinished:edit")
    @Log(title = "产成品对应的半成品", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProductSemifinished productSemifinished)
    {
        return toAjax(productSemifinishedService.updateProductSemifinished(productSemifinished));
    }

    /**
     * 删除产成品对应的半成品
     */
    @RequiresPermissions("consult/basedata:product-semifinished:remove")
    @Log(title = "产成品对应的半成品", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(productSemifinishedService.deleteProductSemifinishedByIds(ids));
    }

    /**
     * 下载模板
     */
   // @RequiresPermissions("consult/basedata:product-semifinished:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<ProductSemifinished> util = new ExcelUtil<ProductSemifinished>(ProductSemifinished.class);
        util.hideColumn("quantity","workshopInventory","productionRequired");
        return util.importTemplateExcel("产成品对应的半成品数据");
    }

    /**
     * 导入数据
     */
   // @RequiresPermissions("consult/basedata:product-semifinished:import")
    @Log(title = "产成品对应的半成品", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<ProductSemifinished> util = new ExcelUtil<ProductSemifinished>(ProductSemifinished.class);
        List<ProductSemifinished> productSemifinishedList = util.importExcel(file.getInputStream());
        String message = importProductSemifinished(dataAnalysisId,productSemifinishedList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入产成品对应的半成品数据
     *
     * @param dataAnalysisId
     * @param productSemifinishedList 产成品对应的半成品数据列表
     * @param isUpdateSupport         是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importProductSemifinished(Long dataAnalysisId, List<ProductSemifinished> productSemifinishedList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(productSemifinishedList) || productSemifinishedList.size() == 0)
        {
            throw new ServiceException("导入产成品对应的半成品数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductSemifinished productSemifinished : productSemifinishedList)
        {
            try
            {
                if(StringUtils.isBlank(productSemifinished.getNeutralCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、半成品编码不能为空");
                }else {
                    productSemifinished.setDataAnalysisId(dataAnalysisId);
                    importDataSave1(productSemifinished);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析 " + productSemifinished.getNeutralCode() + " 导入成功");
                }

                /** 验证是否存在这个产成品对应的半成品
                ProductSemifinished ps = new ProductSemifinished();
                ps.setDataAnalysisId(productSemifinished.getDataAnalysisId());
                ps.setNeutralCode(productSemifinished.getNeutralCode());
                List<ProductSemifinished> list = productSemifinishedService.selectProductSemifinishedList(ps);
                if (list.size() == 0)
                {
                    productSemifinishedService.insertProductSemifinished(productSemifinished);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + productSemifinished.getDataAnalysisId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    productSemifinished.setId(list.get(0).getId());
                    productSemifinishedService.updateProductSemifinished(productSemifinished);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + productSemifinished.getDataAnalysisId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据分析ID " + productSemifinished.getDataAnalysisId() + " 已存在");
                }**/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据分析ID " + productSemifinished.getDataAnalysisId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                logger.error("数据导入异常",e);
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * Excel父子级别格式导入
     * @param productSemifinished
     */
    private void importDataSave1(ProductSemifinished productSemifinished) {
        productSemifinishedService.insertProductSemifinished(productSemifinished);
    }

    /**
     * Excel物料编码横向字段格式导入
     * @param productSemifinished
     */
    private void importDataSave2(ProductSemifinished productSemifinished) {
        List<ProductSemifinished> semifinisheds = new ArrayList<>();

        for (int i = 1; i <= 28; i++) {
            String fieldName = "code" + i;
            try {
                // 获取字段对象
                Field field = ProductSemifinished.class.getDeclaredField(fieldName);
                // 允许访问私有字段
                field.setAccessible(true);
                // 获取字段值
                String bomCode = (String) field.get(productSemifinished);

                if(StringUtils.isNotBlank(bomCode)){
                    ProductSemifinished semifinished = new ProductSemifinished();
                    BeanUtils.copyProperties(productSemifinished,semifinished);
                    semifinished.setMaterialCode(bomCode);
                    semifinisheds.add(semifinished);
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                logger.error("获取字段信息失败",e.getMessage());
            }
        }

        int size = semifinisheds.size();
        for(int i = 0; i < size; i++){
            ProductSemifinished semifinished = semifinisheds.get(i);
            semifinished.setIsMaster(Objects.equals(i,size - 1) ? 1 : 0);
            productSemifinishedService.insertProductSemifinished(semifinished);
        }
    }

    /**
     * 根据数据分析ID删除产成品对应的半成品数据
     */
    @RequiresPermissions("consult/basedata:product-semifinished:remove")
    @Log(title = "产成品对应的半成品", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(productSemifinishedService.deleteProductSemifinishedByDataAnalysisId(dataAnalysisId));
    }
}
