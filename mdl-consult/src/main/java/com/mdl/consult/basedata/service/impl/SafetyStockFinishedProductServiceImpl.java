package com.mdl.consult.basedata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.basedata.mapper.SafetyStockFinishedProductMapper;
import com.mdl.consult.basedata.domain.SafetyStockFinishedProduct;
import com.mdl.consult.basedata.service.ISafetyStockFinishedProductService;
import com.ruoyi.common.core.text.Convert;

/**
 * 产成品安全库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class SafetyStockFinishedProductServiceImpl implements ISafetyStockFinishedProductService
{
    @Autowired
    private SafetyStockFinishedProductMapper safetyStockFinishedProductMapper;

    /**
     * 查询产成品安全库存
     *
     * @param id 产成品安全库存主键
     * @return 产成品安全库存
     */
    @Override
    public SafetyStockFinishedProduct selectSafetyStockFinishedProductById(Long id)
    {
        return safetyStockFinishedProductMapper.selectSafetyStockFinishedProductById(id);
    }

    /**
     * 查询产成品安全库存列表
     *
     * @param safetyStockFinishedProduct 产成品安全库存
     * @return 产成品安全库存
     */
    @Override
    public List<SafetyStockFinishedProduct> selectSafetyStockFinishedProductList(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        return safetyStockFinishedProductMapper.selectSafetyStockFinishedProductList(safetyStockFinishedProduct);
    }

    /**
     * 新增产成品安全库存
     *
     * @param safetyStockFinishedProduct 产成品安全库存
     * @return 结果
     */
    @Override
    public int insertSafetyStockFinishedProduct(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        safetyStockFinishedProduct.setCreateTime(DateUtils.getNowDate());
        return safetyStockFinishedProductMapper.insertSafetyStockFinishedProduct(safetyStockFinishedProduct);
    }

    /**
     * 修改产成品安全库存
     *
     * @param safetyStockFinishedProduct 产成品安全库存
     * @return 结果
     */
    @Override
    public int updateSafetyStockFinishedProduct(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        safetyStockFinishedProduct.setUpdateTime(DateUtils.getNowDate());
        return safetyStockFinishedProductMapper.updateSafetyStockFinishedProduct(safetyStockFinishedProduct);
    }

    /**
     * 批量删除产成品安全库存
     *
     * @param ids 需要删除的产成品安全库存主键
     * @return 结果
     */
    @Override
    public int deleteSafetyStockFinishedProductByIds(String ids)
    {
        return safetyStockFinishedProductMapper.deleteSafetyStockFinishedProductByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除产成品安全库存信息
     *
     * @param id 产成品安全库存主键
     * @return 结果
     */
    @Override
    public int deleteSafetyStockFinishedProductById(Long id)
    {
        return safetyStockFinishedProductMapper.deleteSafetyStockFinishedProductById(id);
    }

    /**
     * 根据数据分析ID删除产成品安全库存数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteSafetyStockFinishedProductByDataAnalysisId(Long dataAnalysisId)
    {
        return safetyStockFinishedProductMapper.deleteSafetyStockFinishedProductByDataAnalysisId(dataAnalysisId);
    }
}
