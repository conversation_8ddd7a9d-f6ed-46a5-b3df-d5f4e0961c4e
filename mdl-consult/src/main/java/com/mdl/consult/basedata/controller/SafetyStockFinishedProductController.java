package com.mdl.consult.basedata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.basedata.domain.SafetyStockFinishedProduct;
import com.mdl.consult.basedata.service.ISafetyStockFinishedProductService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 产成品安全库存Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/basedata/safety-stock/finished-product")
public class SafetyStockFinishedProductController extends BaseController
{
    private String prefix = "consult/basedata/safety-stock/finished-product";

    @Autowired
    private ISafetyStockFinishedProductService safetyStockFinishedProductService;

    @RequiresPermissions("consult/basedata/safety-stock:finished-product:view")
    @GetMapping()
    public String finishedProduct()
    {
        return prefix + "/finished-product";
    }

    /**
     * 查询产成品安全库存列表
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        startPage();
        List<SafetyStockFinishedProduct> list = safetyStockFinishedProductService.selectSafetyStockFinishedProductList(safetyStockFinishedProduct);
        return getDataTable(list);
    }

    /**
     * 导出产成品安全库存列表
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:export")
    @Log(title = "产成品安全库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        List<SafetyStockFinishedProduct> list = safetyStockFinishedProductService.selectSafetyStockFinishedProductList(safetyStockFinishedProduct);
        ExcelUtil<SafetyStockFinishedProduct> util = new ExcelUtil<SafetyStockFinishedProduct>(SafetyStockFinishedProduct.class);
        return util.exportExcel(list, "产成品安全库存数据");
    }

    /**
     * 新增产成品安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存产成品安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:add")
    @Log(title = "产成品安全库存", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        return toAjax(safetyStockFinishedProductService.insertSafetyStockFinishedProduct(safetyStockFinishedProduct));
    }

    /**
     * 修改产成品安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SafetyStockFinishedProduct safetyStockFinishedProduct = safetyStockFinishedProductService.selectSafetyStockFinishedProductById(id);
        mmap.put("safetyStockFinishedProduct", safetyStockFinishedProduct);
        return prefix + "/edit";
    }

    /**
     * 修改保存产成品安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:edit")
    @Log(title = "产成品安全库存", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SafetyStockFinishedProduct safetyStockFinishedProduct)
    {
        return toAjax(safetyStockFinishedProductService.updateSafetyStockFinishedProduct(safetyStockFinishedProduct));
    }

    /**
     * 删除产成品安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:remove")
    @Log(title = "产成品安全库存", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(safetyStockFinishedProductService.deleteSafetyStockFinishedProductByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/basedata/safety-stock:finished-product:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<SafetyStockFinishedProduct> util = new ExcelUtil<SafetyStockFinishedProduct>(SafetyStockFinishedProduct.class);
        return util.importTemplateExcel("产成品安全库存数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/basedata/safety-stock:finished-product:import")
    @Log(title = "产成品安全库存", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<SafetyStockFinishedProduct> util = new ExcelUtil<SafetyStockFinishedProduct>(SafetyStockFinishedProduct.class);
        List<SafetyStockFinishedProduct> safetyStockFinishedProductList = util.importExcel(file.getInputStream());
        String message = importSafetyStockFinishedProduct(dataAnalysisId,safetyStockFinishedProductList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入产成品安全库存数据
     *
     * @param dataAnalysisId
     * @param safetyStockFinishedProductList 产成品安全库存数据列表
     * @param isUpdateSupport                是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importSafetyStockFinishedProduct(Long dataAnalysisId, List<SafetyStockFinishedProduct> safetyStockFinishedProductList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(safetyStockFinishedProductList) || safetyStockFinishedProductList.size() == 0)
        {
            throw new ServiceException("导入产成品安全库存数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SafetyStockFinishedProduct safetyStockFinishedProduct : safetyStockFinishedProductList)
        {
            try
            {
                if(StringUtils.isBlank(safetyStockFinishedProduct.getInventoryCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、存货编码不能为空");
                }else {
                    safetyStockFinishedProduct.setDataAnalysisId(dataAnalysisId);
                    safetyStockFinishedProductService.insertSafetyStockFinishedProduct(safetyStockFinishedProduct);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产成品安全库存 " + safetyStockFinishedProduct.getInventoryName() + " 导入成功");
                }

                /* 验证是否存在这个产成品安全库存
                SafetyStockFinishedProduct existProduct = safetyStockFinishedProductService.selectSafetyStockFinishedProductById(safetyStockFinishedProduct.getId());
                if (StringUtils.isNull(existProduct))
                {
                    safetyStockFinishedProductService.insertSafetyStockFinishedProduct(safetyStockFinishedProduct);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产成品安全库存 " + safetyStockFinishedProduct.getInventoryName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    safetyStockFinishedProductService.updateSafetyStockFinishedProduct(safetyStockFinishedProduct);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产成品安全库存 " + safetyStockFinishedProduct.getInventoryName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、产成品安全库存 " + safetyStockFinishedProduct.getInventoryName() + " 已存在");
                }*/
            }
            catch (Exception e) {
                logger.error("导入数据异常",e);
                failureNum++;
                String msg = "<br/>" + failureNum + "、产成品安全库存 " + safetyStockFinishedProduct != null ? safetyStockFinishedProduct.getInventoryName() : null + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除产成品安全库存数据
     */
    @RequiresPermissions("consult/basedata/safety-stock:finished-product:remove")
    @Log(title = "产成品安全库存", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(safetyStockFinishedProductService.deleteSafetyStockFinishedProductByDataAnalysisId(dataAnalysisId));
    }
}
