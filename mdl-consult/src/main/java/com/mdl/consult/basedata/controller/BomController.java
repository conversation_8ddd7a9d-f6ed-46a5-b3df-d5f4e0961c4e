package com.mdl.consult.basedata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.basedata.domain.Bom;
import com.mdl.consult.basedata.service.IBomService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * BOMController
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/basedata/bom")
public class BomController extends BaseController
{
    private String prefix = "consult/basedata/bom";

    @Autowired
    private IBomService bomService;

    @RequiresPermissions("consult/basedata:bom:view")
    @GetMapping()
    public String bom()
    {
        return prefix + "/bom";
    }

    /**
     * 查询BOM列表
     */
    @RequiresPermissions("consult/basedata:bom:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Bom bom)
    {
        startPage();
        List<Bom> list = bomService.selectBomList(bom);
        return getDataTable(list);
    }

    /**
     * 导出BOM列表
     */
    @RequiresPermissions("consult/basedata:bom:export")
    @Log(title = "BOM", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Bom bom)
    {
        List<Bom> list = bomService.selectBomList(bom);
        ExcelUtil<Bom> util = new ExcelUtil<Bom>(Bom.class);
        return util.exportExcel(list, "BOM数据");
    }

    /**
     * 新增BOM
     */
    @RequiresPermissions("consult/basedata:bom:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存BOM
     */
    @RequiresPermissions("consult/basedata:bom:add")
    @Log(title = "BOM", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Bom bom)
    {
        return toAjax(bomService.insertBom(bom));
    }

    /**
     * 修改BOM
     */
    @RequiresPermissions("consult/basedata:bom:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        Bom bom = bomService.selectBomById(id);
        mmap.put("bom", bom);
        return prefix + "/edit";
    }

    /**
     * 修改保存BOM
     */
    @RequiresPermissions("consult/basedata:bom:edit")
    @Log(title = "BOM", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Bom bom)
    {
        return toAjax(bomService.updateBom(bom));
    }

    /**
     * 删除BOM
     */
    @RequiresPermissions("consult/basedata:bom:remove")
    @Log(title = "BOM", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bomService.deleteBomByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/basedata:bom:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<Bom> util = new ExcelUtil<Bom>(Bom.class);
        return util.importTemplateExcel("BOM数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/basedata:bom:import")
    @Log(title = "BOM", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<Bom> util = new ExcelUtil<Bom>(Bom.class);
        List<Bom> bomList = util.importExcel(file.getInputStream());
        String message = importBom(dataAnalysisId,bomList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入BOM数据
     *
     * @param dataAnalysisId
     * @param bomList         BOM数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importBom(Long dataAnalysisId, List<Bom> bomList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(bomList) || bomList.size() == 0)
        {
            throw new ServiceException("导入BOM数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (Bom bom : bomList)
        {
            try
            {
                if(StringUtils.isBlank(bom.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    bom.setDataAnalysisId(dataAnalysisId);
                    bomService.insertBom(bom);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、BOM " + bom.getMaterialName() + " 导入成功");
                }
                /* 验证是否存在这个BOM
                Bom b = bomService.selectBomById(bom.getId());
                if (StringUtils.isNull(b))
                {
                    bomService.insertBom(bom);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、BOM " + bom.getMaterialName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    bomService.updateBom(bom);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、BOM " + bom.getMaterialName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、BOM " + bom.getMaterialName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、BOM " + bom.getMaterialName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除BOM数据
     */
    @RequiresPermissions("consult/basedata:bom:remove")
    @Log(title = "BOM", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(bomService.deleteBomByDataAnalysisId(dataAnalysisId));
    }
}
