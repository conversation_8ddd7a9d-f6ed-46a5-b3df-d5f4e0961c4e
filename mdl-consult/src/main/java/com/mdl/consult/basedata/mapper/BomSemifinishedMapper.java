package com.mdl.consult.basedata.mapper;

import java.util.List;
import com.mdl.consult.basedata.domain.BomSemifinished;

/**
 * 半成品BOMMapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface BomSemifinishedMapper
{
    /**
     * 查询半成品BOM
     *
     * @param id 半成品BOM主键
     * @return 半成品BOM
     */
    public BomSemifinished selectBomSemifinishedById(Long id);

    /**
     * 查询半成品BOM列表
     *
     * @param bomSemifinished 半成品BOM
     * @return 半成品BOM集合
     */
    public List<BomSemifinished> selectBomSemifinishedList(BomSemifinished bomSemifinished);

    /**
     * 新增半成品BOM
     *
     * @param bomSemifinished 半成品BOM
     * @return 结果
     */
    public int insertBomSemifinished(BomSemifinished bomSemifinished);

    /**
     * 修改半成品BOM
     *
     * @param bomSemifinished 半成品BOM
     * @return 结果
     */
    public int updateBomSemifinished(BomSemifinished bomSemifinished);

    /**
     * 删除半成品BOM
     *
     * @param id 半成品BOM主键
     * @return 结果
     */
    public int deleteBomSemifinishedById(Long id);

    /**
     * 批量删除半成品BOM
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBomSemifinishedByIds(String[] ids);

    /**
     * 根据数据分析ID删除半成品BOM数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteBomSemifinishedByDataAnalysisId(Long dataAnalysisId);
}
