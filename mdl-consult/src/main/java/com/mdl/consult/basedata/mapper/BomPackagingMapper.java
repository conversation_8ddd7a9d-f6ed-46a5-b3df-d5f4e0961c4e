package com.mdl.consult.basedata.mapper;

import java.util.List;
import com.mdl.consult.basedata.domain.BomPackaging;

/**
 * 包装BOMMapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface BomPackagingMapper
{
    /**
     * 查询包装BOM
     *
     * @param id 包装BOM主键
     * @return 包装BOM
     */
    public BomPackaging selectBomPackagingById(Long id);

    /**
     * 查询包装BOM列表
     *
     * @param bomPackaging 包装BOM
     * @return 包装BOM集合
     */
    public List<BomPackaging> selectBomPackagingList(BomPackaging bomPackaging);

    /**
     * 新增包装BOM
     *
     * @param bomPackaging 包装BOM
     * @return 结果
     */
    public int insertBomPackaging(BomPackaging bomPackaging);

    /**
     * 修改包装BOM
     *
     * @param bomPackaging 包装BOM
     * @return 结果
     */
    public int updateBomPackaging(BomPackaging bomPackaging);

    /**
     * 删除包装BOM
     *
     * @param id 包装BOM主键
     * @return 结果
     */
    public int deleteBomPackagingById(Long id);

    /**
     * 批量删除包装BOM
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBomPackagingByIds(String[] ids);

    /**
     * 根据数据分析ID删除包装BOM数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteBomPackagingByDataAnalysisId(Long dataAnalysisId);
}
