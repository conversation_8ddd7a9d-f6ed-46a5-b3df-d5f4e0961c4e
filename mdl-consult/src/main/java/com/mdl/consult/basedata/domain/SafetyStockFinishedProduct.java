package com.mdl.consult.basedata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产成品安全库存对象 mdl_safety_stock_finished_product
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class SafetyStockFinishedProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    private Long dataAnalysisId;

    /** 统计编号 */
    private String statisticsNumber;

    /** 存货编码 */
    @Excel(name = "存货编码")
    private String inventoryCode;

    /** 存货名称 */
    @Excel(name = "存货名称")
    private String inventoryName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 安全库存 */
    @Excel(name = "安全库存")
    private BigDecimal safetyStock;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatisticsNumber(String statisticsNumber) 
    {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() 
    {
        return statisticsNumber;
    }

    public void setInventoryCode(String inventoryCode) 
    {
        this.inventoryCode = inventoryCode;
    }

    public String getInventoryCode() 
    {
        return inventoryCode;
    }

    public void setInventoryName(String inventoryName) 
    {
        this.inventoryName = inventoryName;
    }

    public String getInventoryName() 
    {
        return inventoryName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public void setSafetyStock(BigDecimal safetyStock) 
    {
        this.safetyStock = safetyStock;
    }

    public BigDecimal getSafetyStock() 
    {
        return safetyStock;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("inventoryCode", getInventoryCode())
            .append("inventoryName", getInventoryName())
            .append("specification", getSpecification())
            .append("unit", getUnit())
            .append("safetyStock", getSafetyStock())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public Long getDataAnalysisId(){
        return dataAnalysisId;
}

    public void setDataAnalysisId(Long dataAnalysisId) {
        this.dataAnalysisId = dataAnalysisId;
    }
}
