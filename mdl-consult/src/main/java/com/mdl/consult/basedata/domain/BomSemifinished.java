package com.mdl.consult.basedata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 半成品BOM对象 mdl_bom_semifinished
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class BomSemifinished extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    private Long dataAnalysisId;

    /** 统计编号 */
    private String statisticsNumber;

    /** 母件编码 */
    @Excel(name = "母件编码")
    private String parentCode;

    /** 物料名称 */
    @Excel(name = "母件物料名称")
    private String parentName;

    /** 子件编码 */
    @Excel(name = "子件编码")
    private String childCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String childName;

    /** 规格说明 */
    @Excel(name = "规格说明")
    private String specification;

    /** 子件数量 */
    @Excel(name = "子件数量")
    private BigDecimal childQuantity;

    /** 子件计量单位 */
    @Excel(name = "子件计量单位")
    private String childUnit;

    /** 订单量 */
    @Excel(name = "订单量")
    private BigDecimal orderQuantity;

    /** 物料需求小计 */
    @Excel(name = "物料需求小计")
    private BigDecimal materialRequirementSubtotal;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatisticsNumber(String statisticsNumber) 
    {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() 
    {
        return statisticsNumber;
    }

    public void setParentCode(String parentCode) 
    {
        this.parentCode = parentCode;
    }

    public String getParentCode() 
    {
        return parentCode;
    }

    public void setParentName(String parentName) 
    {
        this.parentName = parentName;
    }

    public String getParentName() 
    {
        return parentName;
    }

    public void setChildCode(String childCode) 
    {
        this.childCode = childCode;
    }

    public String getChildCode() 
    {
        return childCode;
    }

    public void setChildName(String childName) 
    {
        this.childName = childName;
    }

    public String getChildName() 
    {
        return childName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setChildQuantity(BigDecimal childQuantity) 
    {
        this.childQuantity = childQuantity;
    }

    public BigDecimal getChildQuantity() 
    {
        return childQuantity;
    }

    public void setChildUnit(String childUnit) 
    {
        this.childUnit = childUnit;
    }

    public String getChildUnit() 
    {
        return childUnit;
    }

    public void setMaterialRequirementSubtotal(BigDecimal materialRequirementSubtotal) 
    {
        this.materialRequirementSubtotal = materialRequirementSubtotal;
    }

    public BigDecimal getMaterialRequirementSubtotal() 
    {
        return materialRequirementSubtotal;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("parentCode", getParentCode())
            .append("parentName", getParentName())
            .append("childCode", getChildCode())
            .append("childName", getChildName())
            .append("specification", getSpecification())
            .append("childQuantity", getChildQuantity())
            .append("childUnit", getChildUnit())
            .append("materialRequirementSubtotal", getMaterialRequirementSubtotal())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public Long getDataAnalysisId() {
        return dataAnalysisId;
    }

    public void setDataAnalysisId(Long dataAnalysisId) {
        this.dataAnalysisId = dataAnalysisId;
    }
    public BigDecimal getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(BigDecimal orderQuantity) {
        this.orderQuantity = orderQuantity;
    }
}
