package com.mdl.consult.bizdata.entity;

import com.mdl.consult.common.entity.EnumIntegerInterface;

/**
 * <AUTHOR>
 * @Date 2025/6/10
 */
public enum WarehouseType implements EnumIntegerInterface {
    成品仓(1),
    半品仓(2),
    原材料(3);
    private Integer value;

    WarehouseType(Integer value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public static WarehouseType getByValue(Integer value) {
        return EnumIntegerInterface.getByValue(value, WarehouseType.class);
    }
}