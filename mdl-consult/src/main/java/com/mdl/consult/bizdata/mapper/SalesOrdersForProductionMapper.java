package com.mdl.consult.bizdata.mapper;

import java.util.List;
import com.mdl.consult.bizdata.domain.SalesOrdersForProduction;

/**
 * 需要生产的销售订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Deprecated
public interface SalesOrdersForProductionMapper
{
    /**
     * 查询需要生产的销售订单
     *
     * @param id 需要生产的销售订单主键
     * @return 需要生产的销售订单
     */
    public SalesOrdersForProduction selectSalesOrdersForProductionById(Long id);

    /**
     * 查询需要生产的销售订单列表
     *
     * @param salesOrdersForProduction 需要生产的销售订单
     * @return 需要生产的销售订单集合
     */
    public List<SalesOrdersForProduction> selectSalesOrdersForProductionList(SalesOrdersForProduction salesOrdersForProduction);

    /**
     * 新增需要生产的销售订单
     *
     * @param salesOrdersForProduction 需要生产的销售订单
     * @return 结果
     */
    public int insertSalesOrdersForProduction(SalesOrdersForProduction salesOrdersForProduction);

    /**
     * 修改需要生产的销售订单
     *
     * @param salesOrdersForProduction 需要生产的销售订单
     * @return 结果
     */
    public int updateSalesOrdersForProduction(SalesOrdersForProduction salesOrdersForProduction);

    /**
     * 删除需要生产的销售订单
     *
     * @param id 需要生产的销售订单主键
     * @return 结果
     */
    public int deleteSalesOrdersForProductionById(Long id);

    /**
     * 批量删除需要生产的销售订单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSalesOrdersForProductionByIds(String[] ids);

    /**
     * 根据数据分析ID删除需要生产的销售订单数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteSalesOrdersForProductionByDataAnalysisId(Long dataAnalysisId);
}
