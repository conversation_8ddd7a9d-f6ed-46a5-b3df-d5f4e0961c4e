package com.mdl.consult.bizdata.mapper;

import java.util.List;
import com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived;

/**
 * 车间已领Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface MaterialsWorkshopReceivedMapper
{
    /**
     * 查询车间已领
     *
     * @param id 车间已领主键
     * @return 车间已领
     */
    public MaterialsWorkshopReceived selectMaterialsWorkshopReceivedById(Long id);

    /**
     * 查询车间已领列表
     *
     * @param materialsWorkshopReceived 车间已领
     * @return 车间已领集合
     */
    public List<MaterialsWorkshopReceived> selectMaterialsWorkshopReceivedList(MaterialsWorkshopReceived materialsWorkshopReceived);

    /**
     * 新增车间已领
     *
     * @param materialsWorkshopReceived 车间已领
     * @return 结果
     */
    public int insertMaterialsWorkshopReceived(MaterialsWorkshopReceived materialsWorkshopReceived);

    /**
     * 修改车间已领
     *
     * @param materialsWorkshopReceived 车间已领
     * @return 结果
     */
    public int updateMaterialsWorkshopReceived(MaterialsWorkshopReceived materialsWorkshopReceived);

    /**
     * 删除车间已领
     *
     * @param id 车间已领主键
     * @return 结果
     */
    public int deleteMaterialsWorkshopReceivedById(Long id);

    /**
     * 批量删除车间已领
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialsWorkshopReceivedByIds(String[] ids);

    /**
     * 根据数据分析ID删除车间已领数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteMaterialsWorkshopReceivedByDataAnalysisId(Long dataAnalysisId);
}
