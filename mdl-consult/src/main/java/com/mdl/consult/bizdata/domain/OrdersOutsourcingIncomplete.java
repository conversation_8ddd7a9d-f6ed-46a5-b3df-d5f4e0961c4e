package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 未完成委外订单列对象 mdl_orders_outsourcing_incomplete
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class OrdersOutsourcingIncomplete extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    private String statisticsNumber;

    /** 供应商 */
    @Excel(name = "供应商")
    private String supplier;

    /** 委外单号 */
    @Excel(name = "委外订单号")
    private String outsourcingOrderNo;

    /** 订单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "订单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    /** 交货期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "交货期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialLongCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 发出数量 */
    @Excel(name = "发出数量")
    private BigDecimal sentQuantity;

    /** 累计入库数 */
    @Excel(name = "累计入库数")
    private BigDecimal cumulativeStorage;

    /** 未完成入库数 */
    @Excel(name = "未完成入库数")
    private BigDecimal incompleteStorage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setSupplier(String supplier) 
    {
        this.supplier = supplier;
    }

    public String getSupplier() 
    {
        return supplier;
    }

    public void setOutsourcingOrderNo(String outsourcingOrderNo) 
    {
        this.outsourcingOrderNo = outsourcingOrderNo;
    }

    public String getOutsourcingOrderNo() 
    {
        return outsourcingOrderNo;
    }

    public void setOrderDate(Date orderDate) 
    {
        this.orderDate = orderDate;
    }

    public Date getOrderDate() 
    {
        return orderDate;
    }

    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }

    public void setMaterialLongCode(String materialLongCode) 
    {
        this.materialLongCode = materialLongCode;
    }

    public String getMaterialLongCode() 
    {
        return materialLongCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setSentQuantity(BigDecimal sentQuantity) 
    {
        this.sentQuantity = sentQuantity;
    }

    public BigDecimal getSentQuantity() 
    {
        return sentQuantity;
    }

    public void setCumulativeStorage(BigDecimal cumulativeStorage) 
    {
        this.cumulativeStorage = cumulativeStorage;
    }

    public BigDecimal getCumulativeStorage() 
    {
        return cumulativeStorage;
    }

    public void setIncompleteStorage(BigDecimal incompleteStorage) 
    {
        this.incompleteStorage = incompleteStorage;
    }

    public BigDecimal getIncompleteStorage() 
    {
        return incompleteStorage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("supplier", getSupplier())
            .append("outsourcingOrderNo", getOutsourcingOrderNo())
            .append("orderDate", getOrderDate())
            .append("deliveryDate", getDeliveryDate())
            .append("materialLongCode", getMaterialLongCode())
            .append("materialName", getMaterialName())
            .append("unit", getUnit())
            .append("specification", getSpecification())
            .append("sentQuantity", getSentQuantity())
            .append("cumulativeStorage", getCumulativeStorage())
            .append("incompleteStorage", getIncompleteStorage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
