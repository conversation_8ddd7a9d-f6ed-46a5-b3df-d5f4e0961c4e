package com.mdl.consult.bizdata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.DataSumMapper;
import com.mdl.consult.bizdata.entity.WarehouseType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.SalesOrdersIncompleteMapper;
import com.mdl.consult.bizdata.domain.SalesOrdersIncomplete;
import com.mdl.consult.bizdata.service.ISalesOrdersIncompleteService;
import com.ruoyi.common.core.text.Convert;

/**
 * 未完成销售订单列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
@Slf4j
public class SalesOrdersIncompleteServiceImpl implements ISalesOrdersIncompleteService
{
    @Autowired
    private SalesOrdersIncompleteMapper salesOrdersIncompleteMapper;
    @Autowired
    private DataSumMapper materialShortageAnalysisMapper;

    /**
     * 查询未完成销售订单列
     *
     * @param id 未完成销售订单列主键
     * @return 未完成销售订单列
     */
    @Override
    public SalesOrdersIncomplete selectSalesOrdersIncompleteById(Long id)
    {
        return salesOrdersIncompleteMapper.selectSalesOrdersIncompleteById(id);
    }

    /**
     * 查询未完成销售订单列列表
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 未完成销售订单列
     */
    @Override
    public List<SalesOrdersIncomplete> selectSalesOrdersIncompleteList(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        return salesOrdersIncompleteMapper.selectSalesOrdersIncompleteList(salesOrdersIncomplete);
    }

    /**
     * 新增未完成销售订单列
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 结果
     */
    @Override
    public int insertSalesOrdersIncomplete(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        salesOrdersIncomplete.setCreateTime(DateUtils.getNowDate());
        return salesOrdersIncompleteMapper.insertSalesOrdersIncomplete(salesOrdersIncomplete);
    }

    /**
     * 修改未完成销售订单列
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 结果
     */
    @Override
    public int updateSalesOrdersIncomplete(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        salesOrdersIncomplete.setUpdateTime(DateUtils.getNowDate());
        return salesOrdersIncompleteMapper.updateSalesOrdersIncomplete(salesOrdersIncomplete);
    }

    /**
     * 批量删除未完成销售订单列
     *
     * @param ids 需要删除的未完成销售订单列主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersIncompleteByIds(String ids)
    {
        return salesOrdersIncompleteMapper.deleteSalesOrdersIncompleteByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除未完成销售订单列信息
     *
     * @param id 未完成销售订单列主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersIncompleteById(Long id)
    {
        return salesOrdersIncompleteMapper.deleteSalesOrdersIncompleteById(id);
    }

    /**
     * 根据数据分析ID删除未完成销售订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersIncompleteByDataAnalysisId(Long dataAnalysisId)
    {
        return salesOrdersIncompleteMapper.deleteSalesOrdersIncompleteByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(String inventoryCode, List<SalesOrdersIncomplete> ordersIncompletes) {
        if(StringUtils.isBlank(inventoryCode)){
            log.error("未完成订单计算失败，inventoryCode不能为空");
            return;
        }

        if(ordersIncompletes == null || ordersIncompletes.isEmpty()){
            log.error("未完成订单计算失败，ordersIncompletes不能为空");
            return;
        }

        Long dataAnalysisId = ordersIncompletes.get(0).getDataAnalysisId();

        BigDecimal currentBalance = Objects.requireNonNullElse(
                materialShortageAnalysisMapper.getInventory(WarehouseType.成品仓.getValue(),dataAnalysisId,inventoryCode)
                ,new BigDecimal(0));

        BigDecimal safeQuantity = Objects.requireNonNullElse(
                materialShortageAnalysisMapper.getProductSafeInventory(dataAnalysisId,inventoryCode)
                ,new BigDecimal(0));

        for (SalesOrdersIncomplete ordersIncomplete : ordersIncompletes) {
            BigDecimal quantity = Objects.requireNonNullElse(ordersIncomplete.getQuantity(),new BigDecimal(0));
            /** 累计发货数量 */
            BigDecimal cumulativeShipment = Objects.requireNonNullElse(ordersIncomplete.getCumulativeShipment(),new BigDecimal(0));
            /** 未发货数量 */
            BigDecimal unshippedQuantity = quantity.subtract(cumulativeShipment);

            ordersIncomplete.setUnshippedQuantity(unshippedQuantity);
            ordersIncomplete.setSafeQuantity(safeQuantity);

            BigDecimal orderQuantity = unshippedQuantity.add(safeQuantity);

            BigDecimal subtractBalance;
            if(orderQuantity.compareTo(currentBalance) > 0){
                subtractBalance = currentBalance;
                currentBalance = new BigDecimal(0);
            }else{
                subtractBalance = orderQuantity;
                currentBalance = currentBalance.subtract(orderQuantity);
            }

            ordersIncomplete.setCurrentBalance(subtractBalance);
            ordersIncomplete.setMonthlyProductionRequired(orderQuantity.subtract(subtractBalance));

            if(MapUtils.getBoolean(ordersIncomplete.getParams(),"isNew",false)){
                insertSalesOrdersIncomplete(ordersIncomplete);
            }else {
                updateSalesOrdersIncomplete(ordersIncomplete);
            }

            safeQuantity = new BigDecimal(0);
        }

    }
}
