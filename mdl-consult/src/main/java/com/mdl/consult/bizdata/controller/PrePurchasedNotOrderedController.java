package com.mdl.consult.bizdata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered;
import com.mdl.consult.bizdata.service.IPrePurchasedNotOrderedService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 已预购未下采购单Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/purchase-orders/pre-purchased")
public class PrePurchasedNotOrderedController extends BaseController
{
    private String prefix = "consult/bizdata/purchase-orders/pre-purchased";

    @Autowired
    private IPrePurchasedNotOrderedService prePurchasedNotOrderedService;

    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:view")
    @GetMapping()
    public String prePurchased()
    {
        return prefix + "/pre-purchased";
    }

    /**
     * 查询已预购未下采购单列表
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        startPage();
        List<PrePurchasedNotOrdered> list = prePurchasedNotOrderedService.selectPrePurchasedNotOrderedList(prePurchasedNotOrdered);
        return getDataTable(list);
    }

    /**
     * 导出已预购未下采购单列表
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:export")
    @Log(title = "已预购未下采购单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        List<PrePurchasedNotOrdered> list = prePurchasedNotOrderedService.selectPrePurchasedNotOrderedList(prePurchasedNotOrdered);
        ExcelUtil<PrePurchasedNotOrdered> util = new ExcelUtil<PrePurchasedNotOrdered>(PrePurchasedNotOrdered.class);
        return util.exportExcel(list, "已预购未下采购单数据");
    }

    /**
     * 新增已预购未下采购单
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存已预购未下采购单
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:add")
    @Log(title = "已预购未下采购单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        return toAjax(prePurchasedNotOrderedService.insertPrePurchasedNotOrdered(prePurchasedNotOrdered));
    }

    /**
     * 修改已预购未下采购单
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        PrePurchasedNotOrdered prePurchasedNotOrdered = prePurchasedNotOrderedService.selectPrePurchasedNotOrderedById(id);
        mmap.put("prePurchasedNotOrdered", prePurchasedNotOrdered);
        return prefix + "/edit";
    }

    /**
     * 修改保存已预购未下采购单
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:edit")
    @Log(title = "已预购未下采购单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        return toAjax(prePurchasedNotOrderedService.updatePrePurchasedNotOrdered(prePurchasedNotOrdered));
    }

    /**
     * 删除已预购未下采购单
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:remove")
    @Log(title = "已预购未下采购单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(prePurchasedNotOrderedService.deletePrePurchasedNotOrderedByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<PrePurchasedNotOrdered> util = new ExcelUtil<PrePurchasedNotOrdered>(PrePurchasedNotOrdered.class);
        return util.importTemplateExcel("已预购未下采购单数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:import")
    @Log(title = "已预购未下采购单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<PrePurchasedNotOrdered> util = new ExcelUtil<PrePurchasedNotOrdered>(PrePurchasedNotOrdered.class);
        List<PrePurchasedNotOrdered> prePurchasedNotOrderedList = util.importExcel(file.getInputStream());
        String message = importPrePurchasedNotOrdered(dataAnalysisId,prePurchasedNotOrderedList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入已预购未下采购单数据
     *
     * @param dataAnalysisId
     * @param prePurchasedNotOrderedList 已预购未下采购单数据列表
     * @param isUpdateSupport            是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importPrePurchasedNotOrdered(Long dataAnalysisId, List<PrePurchasedNotOrdered> prePurchasedNotOrderedList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(prePurchasedNotOrderedList) || prePurchasedNotOrderedList.size() == 0)
        {
            throw new ServiceException("导入已预购未下采购单数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PrePurchasedNotOrdered prePurchasedNotOrdered : prePurchasedNotOrderedList)
        {
            try
            {
                if(StringUtils.isBlank(prePurchasedNotOrdered.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    prePurchasedNotOrdered.setDataAnalysisId(dataAnalysisId);
                    prePurchasedNotOrderedService.insertPrePurchasedNotOrdered(prePurchasedNotOrdered);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + prePurchasedNotOrdered.getDataAnalysisId() + " 导入成功");
                }

                /* 验证是否存在这个已预购未下采购单
                PrePurchasedNotOrdered ppno = new PrePurchasedNotOrdered();
                ppno.setDataAnalysisId(prePurchasedNotOrdered.getDataAnalysisId());
                ppno.setMaterialCode(prePurchasedNotOrdered.getMaterialCode());
                List<PrePurchasedNotOrdered> list = prePurchasedNotOrderedService.selectPrePurchasedNotOrderedList(ppno);
                if (list.size() == 0)
                {
                    prePurchasedNotOrderedService.insertPrePurchasedNotOrdered(prePurchasedNotOrdered);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + prePurchasedNotOrdered.getDataAnalysisId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    prePurchasedNotOrdered.setId(list.get(0).getId());
                    prePurchasedNotOrderedService.updatePrePurchasedNotOrdered(prePurchasedNotOrdered);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + prePurchasedNotOrdered.getDataAnalysisId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据分析ID " + prePurchasedNotOrdered.getDataAnalysisId() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据分析ID " + prePurchasedNotOrdered.getDataAnalysisId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除已预购未下采购单数据
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:pre-purchased:remove")
    @Log(title = "已预购未下采购单", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(prePurchasedNotOrderedService.deletePrePurchasedNotOrderedByDataAnalysisId(dataAnalysisId));
    }
}
