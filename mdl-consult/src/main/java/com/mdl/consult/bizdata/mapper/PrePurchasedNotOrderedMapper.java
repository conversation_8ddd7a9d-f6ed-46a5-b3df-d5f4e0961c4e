package com.mdl.consult.bizdata.mapper;

import java.util.List;
import com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered;

/**
 * 已预购未下采购单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface PrePurchasedNotOrderedMapper
{
    /**
     * 查询已预购未下采购单
     *
     * @param id 已预购未下采购单主键
     * @return 已预购未下采购单
     */
    public PrePurchasedNotOrdered selectPrePurchasedNotOrderedById(Long id);

    /**
     * 查询已预购未下采购单列表
     *
     * @param prePurchasedNotOrdered 已预购未下采购单
     * @return 已预购未下采购单集合
     */
    public List<PrePurchasedNotOrdered> selectPrePurchasedNotOrderedList(PrePurchasedNotOrdered prePurchasedNotOrdered);

    /**
     * 新增已预购未下采购单
     *
     * @param prePurchasedNotOrdered 已预购未下采购单
     * @return 结果
     */
    public int insertPrePurchasedNotOrdered(PrePurchasedNotOrdered prePurchasedNotOrdered);

    /**
     * 修改已预购未下采购单
     *
     * @param prePurchasedNotOrdered 已预购未下采购单
     * @return 结果
     */
    public int updatePrePurchasedNotOrdered(PrePurchasedNotOrdered prePurchasedNotOrdered);

    /**
     * 删除已预购未下采购单
     *
     * @param id 已预购未下采购单主键
     * @return 结果
     */
    public int deletePrePurchasedNotOrderedById(Long id);

    /**
     * 批量删除已预购未下采购单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePrePurchasedNotOrderedByIds(String[] ids);

    /**
     * 根据数据分析ID删除已预购未下采购单数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deletePrePurchasedNotOrderedByDataAnalysisId(Long dataAnalysisId);
}
