package com.mdl.consult.bizdata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.PrePurchasedNotOrderedMapper;
import com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered;
import com.mdl.consult.bizdata.service.IPrePurchasedNotOrderedService;
import com.ruoyi.common.core.text.Convert;

/**
 * 已预购未下采购单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class PrePurchasedNotOrderedServiceImpl implements IPrePurchasedNotOrderedService
{
    @Autowired
    private PrePurchasedNotOrderedMapper prePurchasedNotOrderedMapper;

    /**
     * 查询已预购未下采购单
     *
     * @param id 已预购未下采购单主键
     * @return 已预购未下采购单
     */
    @Override
    public PrePurchasedNotOrdered selectPrePurchasedNotOrderedById(Long id)
    {
        return prePurchasedNotOrderedMapper.selectPrePurchasedNotOrderedById(id);
    }

    /**
     * 查询已预购未下采购单列表
     *
     * @param prePurchasedNotOrdered 已预购未下采购单
     * @return 已预购未下采购单
     */
    @Override
    public List<PrePurchasedNotOrdered> selectPrePurchasedNotOrderedList(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        return prePurchasedNotOrderedMapper.selectPrePurchasedNotOrderedList(prePurchasedNotOrdered);
    }

    /**
     * 新增已预购未下采购单
     *
     * @param prePurchasedNotOrdered 已预购未下采购单
     * @return 结果
     */
    @Override
    public int insertPrePurchasedNotOrdered(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        prePurchasedNotOrdered.setCreateTime(DateUtils.getNowDate());
        return prePurchasedNotOrderedMapper.insertPrePurchasedNotOrdered(prePurchasedNotOrdered);
    }

    /**
     * 修改已预购未下采购单
     *
     * @param prePurchasedNotOrdered 已预购未下采购单
     * @return 结果
     */
    @Override
    public int updatePrePurchasedNotOrdered(PrePurchasedNotOrdered prePurchasedNotOrdered)
    {
        prePurchasedNotOrdered.setUpdateTime(DateUtils.getNowDate());
        return prePurchasedNotOrderedMapper.updatePrePurchasedNotOrdered(prePurchasedNotOrdered);
    }

    /**
     * 批量删除已预购未下采购单
     *
     * @param ids 需要删除的已预购未下采购单主键
     * @return 结果
     */
    @Override
    public int deletePrePurchasedNotOrderedByIds(String ids)
    {
        return prePurchasedNotOrderedMapper.deletePrePurchasedNotOrderedByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除已预购未下采购单信息
     *
     * @param id 已预购未下采购单主键
     * @return 结果
     */
    @Override
    public int deletePrePurchasedNotOrderedById(Long id)
    {
        return prePurchasedNotOrderedMapper.deletePrePurchasedNotOrderedById(id);
    }

    /**
     * 根据数据分析ID删除已预购未下采购单数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deletePrePurchasedNotOrderedByDataAnalysisId(Long dataAnalysisId)
    {
        return prePurchasedNotOrderedMapper.deletePrePurchasedNotOrderedByDataAnalysisId(dataAnalysisId);
    }
}
