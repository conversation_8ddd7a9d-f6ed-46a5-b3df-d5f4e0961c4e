package com.mdl.consult.bizdata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete;
import com.mdl.consult.bizdata.service.IPurchaseOrdersIncompleteService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 未完成采购订单列Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/purchase-orders/incomplete")
public class PurchaseOrdersIncompleteController extends BaseController
{
    private String prefix = "consult/bizdata/purchase-orders/incomplete";

    @Autowired
    private IPurchaseOrdersIncompleteService purchaseOrdersIncompleteService;

    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:view")
    @GetMapping()
    public String incomplete()
    {
        return prefix + "/incomplete";
    }

    /**
     * 查询未完成采购订单列列表
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        startPage();
        List<PurchaseOrdersIncomplete> list = purchaseOrdersIncompleteService.selectPurchaseOrdersIncompleteList(purchaseOrdersIncomplete);
        return getDataTable(list);
    }

    /**
     * 导出未完成采购订单列列表
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:export")
    @Log(title = "未完成采购订单列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        List<PurchaseOrdersIncomplete> list = purchaseOrdersIncompleteService.selectPurchaseOrdersIncompleteList(purchaseOrdersIncomplete);
        ExcelUtil<PurchaseOrdersIncomplete> util = new ExcelUtil<PurchaseOrdersIncomplete>(PurchaseOrdersIncomplete.class);
        return util.exportExcel(list, "未完成采购订单列数据");
    }

    /**
     * 新增未完成采购订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存未完成采购订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:add")
    @Log(title = "未完成采购订单列", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        return toAjax(purchaseOrdersIncompleteService.insertPurchaseOrdersIncomplete(purchaseOrdersIncomplete));
    }

    /**
     * 修改未完成采购订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        PurchaseOrdersIncomplete purchaseOrdersIncomplete = purchaseOrdersIncompleteService.selectPurchaseOrdersIncompleteById(id);
        mmap.put("purchaseOrdersIncomplete", purchaseOrdersIncomplete);
        return prefix + "/edit";
    }

    /**
     * 修改保存未完成采购订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:edit")
    @Log(title = "未完成采购订单列", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        return toAjax(purchaseOrdersIncompleteService.updatePurchaseOrdersIncomplete(purchaseOrdersIncomplete));
    }

    /**
     * 删除未完成采购订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:remove")
    @Log(title = "未完成采购订单列", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(purchaseOrdersIncompleteService.deletePurchaseOrdersIncompleteByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/purchase-orders:incomplete:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<PurchaseOrdersIncomplete> util = new ExcelUtil<PurchaseOrdersIncomplete>(PurchaseOrdersIncomplete.class);
        util.hideColumn("cumulativeStorage","productionQuantity","unstoredQuantity");
        return util.importTemplateExcel("未完成采购订单列数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/purchase-orders:incomplete:import")
    @Log(title = "未完成采购订单列", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<PurchaseOrdersIncomplete> util = new ExcelUtil<PurchaseOrdersIncomplete>(PurchaseOrdersIncomplete.class);
        List<PurchaseOrdersIncomplete> purchaseOrdersIncompleteList = util.importExcel(file.getInputStream());
        String message = importPurchaseOrdersIncomplete(dataAnalysisId,purchaseOrdersIncompleteList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入未完成采购订单列数据
     *
     * @param dataAnalysisId
     * @param purchaseOrdersIncompleteList 未完成采购订单列数据列表
     * @param isUpdateSupport              是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importPurchaseOrdersIncomplete(Long dataAnalysisId, List<PurchaseOrdersIncomplete> purchaseOrdersIncompleteList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(purchaseOrdersIncompleteList) || purchaseOrdersIncompleteList.size() == 0)
        {
            throw new ServiceException("导入未完成采购订单列数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PurchaseOrdersIncomplete purchaseOrdersIncomplete : purchaseOrdersIncompleteList)
        {
            try
            {
                if(StringUtils.isBlank(purchaseOrdersIncomplete.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    purchaseOrdersIncomplete.setDataAnalysisId(dataAnalysisId);
                    purchaseOrdersIncompleteService.insertPurchaseOrdersIncomplete(purchaseOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、未完成采购订单 " + purchaseOrdersIncomplete.getPurchaseOrderNo() + " 导入成功");
                }

                /* 验证是否存在这个未完成采购订单列
                PurchaseOrdersIncomplete existPurchaseOrder = purchaseOrdersIncompleteService.selectPurchaseOrdersIncompleteById(purchaseOrdersIncomplete.getId());
                if (StringUtils.isNull(existPurchaseOrder))
                {
                    purchaseOrdersIncompleteService.insertPurchaseOrdersIncomplete(purchaseOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、未完成采购订单 " + purchaseOrdersIncomplete.getPurchaseOrderNo() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    purchaseOrdersIncompleteService.updatePurchaseOrdersIncomplete(purchaseOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、未完成采购订单 " + purchaseOrdersIncomplete.getPurchaseOrderNo() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、未完成采购订单 " + purchaseOrdersIncomplete.getPurchaseOrderNo() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、未完成采购订单 " + purchaseOrdersIncomplete.getPurchaseOrderNo() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除未完成采购订单列数据
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:incomplete:remove")
    @Log(title = "未完成采购订单列", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(purchaseOrdersIncompleteService.deletePurchaseOrdersIncompleteByDataAnalysisId(dataAnalysisId));
    }
}
