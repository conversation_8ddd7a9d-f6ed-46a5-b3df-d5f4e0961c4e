package com.mdl.consult.bizdata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.OrdersOutsourcingIncompleteMapper;
import com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete;
import com.mdl.consult.bizdata.service.IOrdersOutsourcingIncompleteService;
import com.ruoyi.common.core.text.Convert;

/**
 * 未完成委外订单列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class OrdersOutsourcingIncompleteServiceImpl implements IOrdersOutsourcingIncompleteService
{
    @Autowired
    private OrdersOutsourcingIncompleteMapper ordersOutsourcingIncompleteMapper;

    /**
     * 查询未完成委外订单列
     *
     * @param id 未完成委外订单列主键
     * @return 未完成委外订单列
     */
    @Override
    public OrdersOutsourcingIncomplete selectOrdersOutsourcingIncompleteById(Long id)
    {
        return ordersOutsourcingIncompleteMapper.selectOrdersOutsourcingIncompleteById(id);
    }

    /**
     * 查询未完成委外订单列列表
     *
     * @param ordersOutsourcingIncomplete 未完成委外订单列
     * @return 未完成委外订单列
     */
    @Override
    public List<OrdersOutsourcingIncomplete> selectOrdersOutsourcingIncompleteList(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        return ordersOutsourcingIncompleteMapper.selectOrdersOutsourcingIncompleteList(ordersOutsourcingIncomplete);
    }

    /**
     * 新增未完成委外订单列
     *
     * @param ordersOutsourcingIncomplete 未完成委外订单列
     * @return 结果
     */
    @Override
    public int insertOrdersOutsourcingIncomplete(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        ordersOutsourcingIncomplete.setCreateTime(DateUtils.getNowDate());
        return ordersOutsourcingIncompleteMapper.insertOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete);
    }

    /**
     * 修改未完成委外订单列
     *
     * @param ordersOutsourcingIncomplete 未完成委外订单列
     * @return 结果
     */
    @Override
    public int updateOrdersOutsourcingIncomplete(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        ordersOutsourcingIncomplete.setUpdateTime(DateUtils.getNowDate());
        return ordersOutsourcingIncompleteMapper.updateOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete);
    }

    /**
     * 批量删除未完成委外订单列
     *
     * @param ids 需要删除的未完成委外订单列主键
     * @return 结果
     */
    @Override
    public int deleteOrdersOutsourcingIncompleteByIds(String ids)
    {
        return ordersOutsourcingIncompleteMapper.deleteOrdersOutsourcingIncompleteByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除未完成委外订单列信息
     *
     * @param id 未完成委外订单列主键
     * @return 结果
     */
    @Override
    public int deleteOrdersOutsourcingIncompleteById(Long id)
    {
        return ordersOutsourcingIncompleteMapper.deleteOrdersOutsourcingIncompleteById(id);
    }

    /**
     * 根据数据分析ID删除未完成委外订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteOrdersOutsourcingIncompleteByDataAnalysisId(Long dataAnalysisId)
    {
        return ordersOutsourcingIncompleteMapper.deleteOrdersOutsourcingIncompleteByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete) {

    }
}
