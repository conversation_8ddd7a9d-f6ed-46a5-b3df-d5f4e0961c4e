package com.mdl.consult.bizdata.service;

import java.util.List;
import com.mdl.consult.bizdata.domain.InventoryGoods;

/**
 * 库存商品数Service接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface IInventoryGoodsService
{
    /**
     * 查询库存商品数
     *
     * @param id 库存商品数主键
     * @return 库存商品数
     */
    public InventoryGoods selectInventoryGoodsById(Long id);

    /**
     * 查询库存商品数列表
     *
     * @param inventoryGoods 库存商品数
     * @return 库存商品数集合
     */
    public List<InventoryGoods> selectInventoryGoodsList(InventoryGoods inventoryGoods);

    /**
     * 新增库存商品数
     *
     * @param inventoryGoods 库存商品数
     * @return 结果
     */
    public int insertInventoryGoods(InventoryGoods inventoryGoods);

    /**
     * 修改库存商品数
     *
     * @param inventoryGoods 库存商品数
     * @return 结果
     */
    public int updateInventoryGoods(InventoryGoods inventoryGoods);

    /**
     * 批量删除库存商品数
     *
     * @param ids 需要删除的库存商品数主键集合
     * @return 结果
     */
    public int deleteInventoryGoodsByIds(String ids);

    /**
     * 删除库存商品数信息
     *
     * @param id 库存商品数主键
     * @return 结果
     */
    public int deleteInventoryGoodsById(Long id);

    /**
     * 根据数据分析ID删除库存商品数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteInventoryGoodsByDataAnalysisId(Long dataAnalysisId);
}
