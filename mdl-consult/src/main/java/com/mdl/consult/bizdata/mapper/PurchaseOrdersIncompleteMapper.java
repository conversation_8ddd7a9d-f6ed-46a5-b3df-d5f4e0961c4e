package com.mdl.consult.bizdata.mapper;

import java.util.List;
import com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete;

/**
 * 未完成采购订单列Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface PurchaseOrdersIncompleteMapper
{
    /**
     * 查询未完成采购订单列
     *
     * @param id 未完成采购订单列主键
     * @return 未完成采购订单列
     */
    public PurchaseOrdersIncomplete selectPurchaseOrdersIncompleteById(Long id);

    /**
     * 查询未完成采购订单列列表
     *
     * @param purchaseOrdersIncomplete 未完成采购订单列
     * @return 未完成采购订单列集合
     */
    public List<PurchaseOrdersIncomplete> selectPurchaseOrdersIncompleteList(PurchaseOrdersIncomplete purchaseOrdersIncomplete);

    /**
     * 新增未完成采购订单列
     *
     * @param purchaseOrdersIncomplete 未完成采购订单列
     * @return 结果
     */
    public int insertPurchaseOrdersIncomplete(PurchaseOrdersIncomplete purchaseOrdersIncomplete);

    /**
     * 修改未完成采购订单列
     *
     * @param purchaseOrdersIncomplete 未完成采购订单列
     * @return 结果
     */
    public int updatePurchaseOrdersIncomplete(PurchaseOrdersIncomplete purchaseOrdersIncomplete);

    /**
     * 删除未完成采购订单列
     *
     * @param id 未完成采购订单列主键
     * @return 结果
     */
    public int deletePurchaseOrdersIncompleteById(Long id);

    /**
     * 批量删除未完成采购订单列
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePurchaseOrdersIncompleteByIds(String[] ids);

    /**
     * 根据数据分析ID删除未完成采购订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deletePurchaseOrdersIncompleteByDataAnalysisId(Long dataAnalysisId);
}
