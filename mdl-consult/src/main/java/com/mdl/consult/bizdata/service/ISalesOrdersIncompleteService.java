package com.mdl.consult.bizdata.service;

import java.util.List;
import com.mdl.consult.bizdata.domain.SalesOrdersIncomplete;

/**
 * 未完成销售订单列Service接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface ISalesOrdersIncompleteService
{
    /**
     * 查询未完成销售订单列
     *
     * @param id 未完成销售订单列主键
     * @return 未完成销售订单列
     */
    public SalesOrdersIncomplete selectSalesOrdersIncompleteById(Long id);

    /**
     * 查询未完成销售订单列列表
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 未完成销售订单列集合
     */
    public List<SalesOrdersIncomplete> selectSalesOrdersIncompleteList(SalesOrdersIncomplete salesOrdersIncomplete);

    /**
     * 新增未完成销售订单列
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 结果
     */
    public int insertSalesOrdersIncomplete(SalesOrdersIncomplete salesOrdersIncomplete);

    /**
     * 修改未完成销售订单列
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 结果
     */
    public int updateSalesOrdersIncomplete(SalesOrdersIncomplete salesOrdersIncomplete);

    /**
     * 批量删除未完成销售订单列
     *
     * @param ids 需要删除的未完成销售订单列主键集合
     * @return 结果
     */
    public int deleteSalesOrdersIncompleteByIds(String ids);

    /**
     * 删除未完成销售订单列信息
     *
     * @param id 未完成销售订单列主键
     * @return 结果
     */
    public int deleteSalesOrdersIncompleteById(Long id);

    /**
     * 根据数据分析ID删除未完成销售订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteSalesOrdersIncompleteByDataAnalysisId(Long dataAnalysisId);

    void calData(String inventoryCode, List<SalesOrdersIncomplete> ordersIncompletes);
}
