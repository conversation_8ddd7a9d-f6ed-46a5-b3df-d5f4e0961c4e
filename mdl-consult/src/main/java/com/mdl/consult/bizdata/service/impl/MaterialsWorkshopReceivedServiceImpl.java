package com.mdl.consult.bizdata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.MaterialsWorkshopReceivedMapper;
import com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived;
import com.mdl.consult.bizdata.service.IMaterialsWorkshopReceivedService;
import com.ruoyi.common.core.text.Convert;

/**
 * 车间已领Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class MaterialsWorkshopReceivedServiceImpl implements IMaterialsWorkshopReceivedService
{
    @Autowired
    private MaterialsWorkshopReceivedMapper materialsWorkshopReceivedMapper;

    /**
     * 查询车间已领
     *
     * @param id 车间已领主键
     * @return 车间已领
     */
    @Override
    public MaterialsWorkshopReceived selectMaterialsWorkshopReceivedById(Long id)
    {
        return materialsWorkshopReceivedMapper.selectMaterialsWorkshopReceivedById(id);
    }

    /**
     * 查询车间已领列表
     *
     * @param materialsWorkshopReceived 车间已领
     * @return 车间已领
     */
    @Override
    public List<MaterialsWorkshopReceived> selectMaterialsWorkshopReceivedList(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        return materialsWorkshopReceivedMapper.selectMaterialsWorkshopReceivedList(materialsWorkshopReceived);
    }

    /**
     * 新增车间已领
     *
     * @param materialsWorkshopReceived 车间已领
     * @return 结果
     */
    @Override
    public int insertMaterialsWorkshopReceived(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        materialsWorkshopReceived.setCreateTime(DateUtils.getNowDate());
        return materialsWorkshopReceivedMapper.insertMaterialsWorkshopReceived(materialsWorkshopReceived);
    }

    /**
     * 修改车间已领
     *
     * @param materialsWorkshopReceived 车间已领
     * @return 结果
     */
    @Override
    public int updateMaterialsWorkshopReceived(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        materialsWorkshopReceived.setUpdateTime(DateUtils.getNowDate());
        return materialsWorkshopReceivedMapper.updateMaterialsWorkshopReceived(materialsWorkshopReceived);
    }

    /**
     * 批量删除车间已领
     *
     * @param ids 需要删除的车间已领主键
     * @return 结果
     */
    @Override
    public int deleteMaterialsWorkshopReceivedByIds(String ids)
    {
        return materialsWorkshopReceivedMapper.deleteMaterialsWorkshopReceivedByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除车间已领信息
     *
     * @param id 车间已领主键
     * @return 结果
     */
    @Override
    public int deleteMaterialsWorkshopReceivedById(Long id)
    {
        return materialsWorkshopReceivedMapper.deleteMaterialsWorkshopReceivedById(id);
    }

    /**
     * 根据数据分析ID删除车间已领数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteMaterialsWorkshopReceivedByDataAnalysisId(Long dataAnalysisId)
    {
        return materialsWorkshopReceivedMapper.deleteMaterialsWorkshopReceivedByDataAnalysisId(dataAnalysisId);
    }
}
