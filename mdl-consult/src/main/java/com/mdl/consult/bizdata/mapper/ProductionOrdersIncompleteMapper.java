package com.mdl.consult.bizdata.mapper;

import java.util.List;
import com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete;

/**
 * 未完成生产订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface ProductionOrdersIncompleteMapper
{
    /**
     * 查询未完成生产订单
     *
     * @param id 未完成生产订单主键
     * @return 未完成生产订单
     */
    public ProductionOrdersIncomplete selectProductionOrdersIncompleteById(Long id);

    /**
     * 查询未完成生产订单列表
     *
     * @param productionOrdersIncomplete 未完成生产订单
     * @return 未完成生产订单集合
     */
    public List<ProductionOrdersIncomplete> selectProductionOrdersIncompleteList(ProductionOrdersIncomplete productionOrdersIncomplete);

    /**
     * 新增未完成生产订单
     *
     * @param productionOrdersIncomplete 未完成生产订单
     * @return 结果
     */
    public int insertProductionOrdersIncomplete(ProductionOrdersIncomplete productionOrdersIncomplete);

    /**
     * 修改未完成生产订单
     *
     * @param productionOrdersIncomplete 未完成生产订单
     * @return 结果
     */
    public int updateProductionOrdersIncomplete(ProductionOrdersIncomplete productionOrdersIncomplete);

    /**
     * 删除未完成生产订单
     *
     * @param id 未完成生产订单主键
     * @return 结果
     */
    public int deleteProductionOrdersIncompleteById(Long id);

    /**
     * 批量删除未完成生产订单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProductionOrdersIncompleteByIds(String[] ids);

    /**
     * 根据数据分析ID删除未完成生产订单数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteProductionOrdersIncompleteByDataAnalysisId(Long dataAnalysisId);
}
