package com.mdl.consult.bizdata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.PurchaseOrdersIncompleteMapper;
import com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete;
import com.mdl.consult.bizdata.service.IPurchaseOrdersIncompleteService;
import com.ruoyi.common.core.text.Convert;

/**
 * 未完成采购订单列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class PurchaseOrdersIncompleteServiceImpl implements IPurchaseOrdersIncompleteService
{
    @Autowired
    private PurchaseOrdersIncompleteMapper purchaseOrdersIncompleteMapper;

    /**
     * 查询未完成采购订单列
     *
     * @param id 未完成采购订单列主键
     * @return 未完成采购订单列
     */
    @Override
    public PurchaseOrdersIncomplete selectPurchaseOrdersIncompleteById(Long id)
    {
        return purchaseOrdersIncompleteMapper.selectPurchaseOrdersIncompleteById(id);
    }

    /**
     * 查询未完成采购订单列列表
     *
     * @param purchaseOrdersIncomplete 未完成采购订单列
     * @return 未完成采购订单列
     */
    @Override
    public List<PurchaseOrdersIncomplete> selectPurchaseOrdersIncompleteList(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        return purchaseOrdersIncompleteMapper.selectPurchaseOrdersIncompleteList(purchaseOrdersIncomplete);
    }

    /**
     * 新增未完成采购订单列
     *
     * @param purchaseOrdersIncomplete 未完成采购订单列
     * @return 结果
     */
    @Override
    public int insertPurchaseOrdersIncomplete(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        purchaseOrdersIncomplete.setCreateTime(DateUtils.getNowDate());
        return purchaseOrdersIncompleteMapper.insertPurchaseOrdersIncomplete(purchaseOrdersIncomplete);
    }

    /**
     * 修改未完成采购订单列
     *
     * @param purchaseOrdersIncomplete 未完成采购订单列
     * @return 结果
     */
    @Override
    public int updatePurchaseOrdersIncomplete(PurchaseOrdersIncomplete purchaseOrdersIncomplete)
    {
        purchaseOrdersIncomplete.setUpdateTime(DateUtils.getNowDate());
        return purchaseOrdersIncompleteMapper.updatePurchaseOrdersIncomplete(purchaseOrdersIncomplete);
    }

    /**
     * 批量删除未完成采购订单列
     *
     * @param ids 需要删除的未完成采购订单列主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrdersIncompleteByIds(String ids)
    {
        return purchaseOrdersIncompleteMapper.deletePurchaseOrdersIncompleteByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除未完成采购订单列信息
     *
     * @param id 未完成采购订单列主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrdersIncompleteById(Long id)
    {
        return purchaseOrdersIncompleteMapper.deletePurchaseOrdersIncompleteById(id);
    }

    /**
     * 根据数据分析ID删除未完成采购订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deletePurchaseOrdersIncompleteByDataAnalysisId(Long dataAnalysisId)
    {
        return purchaseOrdersIncompleteMapper.deletePurchaseOrdersIncompleteByDataAnalysisId(dataAnalysisId);
    }
}
