package com.mdl.consult.bizdata.controller;

import java.util.List;

import com.mdl.consult.analysis.mapper.DataSumMapper;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete;
import com.mdl.consult.bizdata.service.IProductionOrdersIncompleteService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 未完成生产订单Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/production-orders/incomplete")
public class ProductionOrdersIncompleteController extends BaseController
{
    @Autowired
    private DataSumMapper materialShortageAnalysisMapper;
    private String prefix = "consult/bizdata/production-orders/incomplete";

    @Autowired
    private IProductionOrdersIncompleteService productionOrdersIncompleteService;

    @RequiresPermissions("consult/bizdata/production-orders:incomplete:view")
    @GetMapping()
    public String incomplete()
    {
        return prefix + "/incomplete";
    }

    /**
     * 查询未完成生产订单列表
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        startPage();
        List<ProductionOrdersIncomplete> list = productionOrdersIncompleteService.selectProductionOrdersIncompleteList(productionOrdersIncomplete);
        return getDataTable(list);
    }

    /**
     * 导出未完成生产订单列表
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:export")
    @Log(title = "未完成生产订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        List<ProductionOrdersIncomplete> list = productionOrdersIncompleteService.selectProductionOrdersIncompleteList(productionOrdersIncomplete);
        ExcelUtil<ProductionOrdersIncomplete> util = new ExcelUtil<ProductionOrdersIncomplete>(ProductionOrdersIncomplete.class);
        return util.exportExcel(list, "未完成生产订单数据");
    }

    /**
     * 新增未完成生产订单
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存未完成生产订单
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:add")
    @Log(title = "未完成生产订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        return toAjax(productionOrdersIncompleteService.insertProductionOrdersIncomplete(productionOrdersIncomplete));
    }

    /**
     * 修改未完成生产订单
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        ProductionOrdersIncomplete productionOrdersIncomplete = productionOrdersIncompleteService.selectProductionOrdersIncompleteById(id);
        mmap.put("productionOrdersIncomplete", productionOrdersIncomplete);
        return prefix + "/edit";
    }

    /**
     * 修改保存未完成生产订单
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:edit")
    @Log(title = "未完成生产订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        return toAjax(productionOrdersIncompleteService.updateProductionOrdersIncomplete(productionOrdersIncomplete));
    }

    /**
     * 删除未完成生产订单
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:remove")
    @Log(title = "未完成生产订单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(productionOrdersIncompleteService.deleteProductionOrdersIncompleteByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/production-orders:incomplete:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<ProductionOrdersIncomplete> util = new ExcelUtil<ProductionOrdersIncomplete>(ProductionOrdersIncomplete.class);
        util.hideColumn("productionQuantity","unstoredQuantity");
        return util.importTemplateExcel("未完成生产订单数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/production-orders:incomplete:import")
    @Log(title = "未完成生产订单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<ProductionOrdersIncomplete> util = new ExcelUtil<ProductionOrdersIncomplete>(ProductionOrdersIncomplete.class);
        List<ProductionOrdersIncomplete> productionOrdersIncompleteList = util.importExcel(file.getInputStream());
        String message = importProductionOrdersIncomplete(dataAnalysisId,productionOrdersIncompleteList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入未完成生产订单数据
     *
     * @param dataAnalysisId
     * @param productionOrdersIncompleteList 未完成生产订单数据列表
     * @param isUpdateSupport                是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importProductionOrdersIncomplete(Long dataAnalysisId, List<ProductionOrdersIncomplete> productionOrdersIncompleteList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(productionOrdersIncompleteList) || productionOrdersIncompleteList.size() == 0)
        {
            throw new ServiceException("导入未完成生产订单数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductionOrdersIncomplete productionOrdersIncomplete : productionOrdersIncompleteList)
        {
            try
            {
                if(StringUtils.isBlank(productionOrdersIncomplete.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    productionOrdersIncomplete.setDataAnalysisId(dataAnalysisId);
                    productionOrdersIncompleteService.insertProductionOrdersIncomplete(productionOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、未完成生产订单 " + productionOrdersIncomplete.getProductionOrderNo() + " 导入成功");
                }

                /* 验证是否存在这个未完成生产订单
                ProductionOrdersIncomplete existOrder = productionOrdersIncompleteService.selectProductionOrdersIncompleteById(productionOrdersIncomplete.getId());
                if (StringUtils.isNull(existOrder))
                {
                    productionOrdersIncompleteService.insertProductionOrdersIncomplete(productionOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、未完成生产订单 " + productionOrdersIncomplete.getProductionOrderNo() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    productionOrdersIncompleteService.updateProductionOrdersIncomplete(productionOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、未完成生产订单 " + productionOrdersIncomplete.getProductionOrderNo() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、未完成生产订单 " + productionOrdersIncomplete.getProductionOrderNo() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、未完成生产订单 " + productionOrdersIncomplete.getProductionOrderNo() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除未完成生产订单数据
     */
    @RequiresPermissions("consult/bizdata/production-orders:incomplete:remove")
    @Log(title = "未完成生产订单", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(productionOrdersIncompleteService.deleteProductionOrdersIncompleteByDataAnalysisId(dataAnalysisId));
    }
}
