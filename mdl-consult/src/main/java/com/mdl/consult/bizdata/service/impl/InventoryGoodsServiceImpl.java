package com.mdl.consult.bizdata.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.InventoryGoodsMapper;
import com.mdl.consult.bizdata.domain.InventoryGoods;
import com.mdl.consult.bizdata.service.IInventoryGoodsService;
import com.ruoyi.common.core.text.Convert;

/**
 * 库存商品数Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class InventoryGoodsServiceImpl implements IInventoryGoodsService
{
    @Autowired
    private InventoryGoodsMapper inventoryGoodsMapper;

    /**
     * 查询库存商品数
     *
     * @param id 库存商品数主键
     * @return 库存商品数
     */
    @Override
    public InventoryGoods selectInventoryGoodsById(Long id)
    {
        return inventoryGoodsMapper.selectInventoryGoodsById(id);
    }

    /**
     * 查询库存商品数列表
     *
     * @param inventoryGoods 库存商品数
     * @return 库存商品数
     */
    @Override
    public List<InventoryGoods> selectInventoryGoodsList(InventoryGoods inventoryGoods)
    {
        return inventoryGoodsMapper.selectInventoryGoodsList(inventoryGoods);
    }

    /**
     * 新增库存商品数
     *
     * @param inventoryGoods 库存商品数
     * @return 结果
     */
    @Override
    public int insertInventoryGoods(InventoryGoods inventoryGoods)
    {
        inventoryGoods.setCreateTime(DateUtils.getNowDate());
        return inventoryGoodsMapper.insertInventoryGoods(inventoryGoods);
    }

    /**
     * 修改库存商品数
     *
     * @param inventoryGoods 库存商品数
     * @return 结果
     */
    @Override
    public int updateInventoryGoods(InventoryGoods inventoryGoods)
    {
        inventoryGoods.setUpdateTime(DateUtils.getNowDate());
        return inventoryGoodsMapper.updateInventoryGoods(inventoryGoods);
    }

    /**
     * 批量删除库存商品数
     *
     * @param ids 需要删除的库存商品数主键
     * @return 结果
     */
    @Override
    public int deleteInventoryGoodsByIds(String ids)
    {
        return inventoryGoodsMapper.deleteInventoryGoodsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除库存商品数信息
     *
     * @param id 库存商品数主键
     * @return 结果
     */
    @Override
    public int deleteInventoryGoodsById(Long id)
    {
        return inventoryGoodsMapper.deleteInventoryGoodsById(id);
    }

    /**
     * 根据数据分析ID删除库存商品数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteInventoryGoodsByDataAnalysisId(Long dataAnalysisId)
    {
        return inventoryGoodsMapper.deleteInventoryGoodsByDataAnalysisId(dataAnalysisId);
    }
}
