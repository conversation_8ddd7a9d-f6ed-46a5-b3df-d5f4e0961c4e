package com.mdl.consult.bizdata.controller;

import java.util.List;

import com.mdl.consult.bizdata.entity.WarehouseType;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.InventoryGoods;
import com.mdl.consult.bizdata.service.IInventoryGoodsService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 库存商品数Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/goods")
public class InventoryGoodsController extends BaseController
{
    private String prefix = "consult/bizdata/goods";

    @Autowired
    private IInventoryGoodsService inventoryGoodsService;

    @RequiresPermissions("consult/bizdata:goods:view")
    @GetMapping()
    public String goods()
    {
        return prefix + "/goods";
    }

    /**
     * 查询库存商品数列表
     */
    @RequiresPermissions("consult/bizdata:goods:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(InventoryGoods inventoryGoods)
    {
        startPage();
        List<InventoryGoods> list = inventoryGoodsService.selectInventoryGoodsList(inventoryGoods);
        return getDataTable(list);
    }

    /**
     * 导出库存商品数列表
     */
    @RequiresPermissions("consult/bizdata:goods:export")
    @Log(title = "库存商品数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(InventoryGoods inventoryGoods)
    {
        List<InventoryGoods> list = inventoryGoodsService.selectInventoryGoodsList(inventoryGoods);
        ExcelUtil<InventoryGoods> util = new ExcelUtil<InventoryGoods>(InventoryGoods.class);
        return util.exportExcel(list, "库存商品数数据");
    }

    /**
     * 新增库存商品数
     */
    @RequiresPermissions("consult/bizdata:goods:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存库存商品数
     */
    @RequiresPermissions("consult/bizdata:goods:add")
    @Log(title = "库存商品数", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(InventoryGoods inventoryGoods)
    {
        return toAjax(inventoryGoodsService.insertInventoryGoods(inventoryGoods));
    }

    /**
     * 修改库存商品数
     */
    @RequiresPermissions("consult/bizdata:goods:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        InventoryGoods inventoryGoods = inventoryGoodsService.selectInventoryGoodsById(id);
        mmap.put("inventoryGoods", inventoryGoods);
        return prefix + "/edit";
    }

    /**
     * 修改保存库存商品数
     */
    @RequiresPermissions("consult/bizdata:goods:edit")
    @Log(title = "库存商品数", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(InventoryGoods inventoryGoods)
    {
        return toAjax(inventoryGoodsService.updateInventoryGoods(inventoryGoods));
    }

    /**
     * 删除库存商品数
     */
    @RequiresPermissions("consult/bizdata:goods:remove")
    @Log(title = "库存商品数", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(inventoryGoodsService.deleteInventoryGoodsByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata:goods:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<InventoryGoods> util = new ExcelUtil<InventoryGoods>(InventoryGoods.class);
        return util.importTemplateExcel("库存商品数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata:goods:import")
    @Log(title = "库存商品数", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<InventoryGoods> util = new ExcelUtil<InventoryGoods>(InventoryGoods.class);
        List<InventoryGoods> inventoryGoodsList = util.importExcel(file.getInputStream());
        String message = importInventoryGoods(dataAnalysisId,inventoryGoodsList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入库存商品数据
     *
     * @param dataAnalysisId
     * @param inventoryGoodsList 库存商品数据列表
     * @param isUpdateSupport    是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importInventoryGoods(Long dataAnalysisId, List<InventoryGoods> inventoryGoodsList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(inventoryGoodsList) || inventoryGoodsList.size() == 0)
        {
            throw new ServiceException("导入库存商品数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (InventoryGoods inventoryGoods : inventoryGoodsList)
        {
            try
            {
                if(StringUtils.isBlank(inventoryGoods.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    inventoryGoods.setDataAnalysisId(dataAnalysisId);
                    if(StringUtils.isNotBlank(inventoryGoods.getWarehouse())){
                        if(inventoryGoods.getWarehouse().indexOf("车间") > -1){
                            inventoryGoods.setWarehouseType(WarehouseType.半品仓.getValue());
                        }

                        if(inventoryGoods.getWarehouse().indexOf("成品") > -1){
                            inventoryGoods.setWarehouseType(WarehouseType.成品仓.getValue());
                        }

                        if(inventoryGoods.getWarehouse().indexOf("材料") > -1){
                            inventoryGoods.setWarehouseType(WarehouseType.原材料.getValue());
                        }
                    }
                    inventoryGoodsService.insertInventoryGoods(inventoryGoods);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、库存商品 " + inventoryGoods.getMaterialName() + " 导入成功");
                }

                /* 验证是否存在这个库存商品
                InventoryGoods existGoods = inventoryGoodsService.selectInventoryGoodsById(inventoryGoods.getId());
                if (StringUtils.isNull(existGoods))
                {
                    inventoryGoodsService.insertInventoryGoods(inventoryGoods);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、库存商品 " + inventoryGoods.getMaterialName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    inventoryGoodsService.updateInventoryGoods(inventoryGoods);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、库存商品 " + inventoryGoods.getMaterialName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、库存商品 " + inventoryGoods.getMaterialName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、库存商品 " + inventoryGoods.getMaterialName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除库存商品数据
     */
    @RequiresPermissions("consult/bizdata:goods:remove")
    @Log(title = "库存商品数", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(inventoryGoodsService.deleteInventoryGoodsByDataAnalysisId(dataAnalysisId));
    }
}
