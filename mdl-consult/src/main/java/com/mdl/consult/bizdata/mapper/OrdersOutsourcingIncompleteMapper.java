package com.mdl.consult.bizdata.mapper;

import java.util.List;
import com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete;

/**
 * 未完成委外订单列Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface OrdersOutsourcingIncompleteMapper
{
    /**
     * 查询未完成委外订单列
     *
     * @param id 未完成委外订单列主键
     * @return 未完成委外订单列
     */
    public OrdersOutsourcingIncomplete selectOrdersOutsourcingIncompleteById(Long id);

    /**
     * 查询未完成委外订单列列表
     *
     * @param ordersOutsourcingIncomplete 未完成委外订单列
     * @return 未完成委外订单列集合
     */
    public List<OrdersOutsourcingIncomplete> selectOrdersOutsourcingIncompleteList(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete);

    /**
     * 新增未完成委外订单列
     *
     * @param ordersOutsourcingIncomplete 未完成委外订单列
     * @return 结果
     */
    public int insertOrdersOutsourcingIncomplete(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete);

    /**
     * 修改未完成委外订单列
     *
     * @param ordersOutsourcingIncomplete 未完成委外订单列
     * @return 结果
     */
    public int updateOrdersOutsourcingIncomplete(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete);

    /**
     * 删除未完成委外订单列
     *
     * @param id 未完成委外订单列主键
     * @return 结果
     */
    public int deleteOrdersOutsourcingIncompleteById(Long id);

    /**
     * 批量删除未完成委外订单列
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrdersOutsourcingIncompleteByIds(String[] ids);

    /**
     * 根据数据分析ID删除未完成委外订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    public int deleteOrdersOutsourcingIncompleteByDataAnalysisId(Long dataAnalysisId);
}
