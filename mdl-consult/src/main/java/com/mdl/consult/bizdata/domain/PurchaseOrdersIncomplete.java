package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 未完成采购订单列对象 mdl_purchase_orders_incomplete
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class PurchaseOrdersIncomplete extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    private String statisticsNumber;

    /** 单据日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "单据日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date documentDate;

    /** 交货期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "交货期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 采购单号 */
    @Excel(name = "采购订单号")
    private String purchaseOrderNo;

    /** 交易类型 */
    @Excel(name = "交易类型")
    private String transactionType;

    /** 供货供应商 */
    @Excel(name = "供货供应商")
    private String supplier;

    /** 单据状态 */
    @Excel(name = "单据状态")
    private String documentStatus;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格说明 */
    @Excel(name = "规格说明")
    private String specification;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal quantity;

    /** 累计入库数量 */
    @Excel(name = "累计入库数量")
    private BigDecimal cumulativeStorage;

    /** 未入库数 */
    @Excel(name = "未入库数")
    private BigDecimal unstoredQuantity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setDocumentDate(Date documentDate) 
    {
        this.documentDate = documentDate;
    }

    public Date getDocumentDate() 
    {
        return documentDate;
    }

    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) 
    {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getPurchaseOrderNo() 
    {
        return purchaseOrderNo;
    }

    public void setTransactionType(String transactionType) 
    {
        this.transactionType = transactionType;
    }

    public String getTransactionType() 
    {
        return transactionType;
    }

    public void setSupplier(String supplier) 
    {
        this.supplier = supplier;
    }

    public String getSupplier() 
    {
        return supplier;
    }

    public void setDocumentStatus(String documentStatus) 
    {
        this.documentStatus = documentStatus;
    }

    public String getDocumentStatus() 
    {
        return documentStatus;
    }

    public void setMaterialCode(String materialCode) 
    {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() 
    {
        return materialCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }

    public void setCumulativeStorage(BigDecimal cumulativeStorage) 
    {
        this.cumulativeStorage = cumulativeStorage;
    }

    public BigDecimal getCumulativeStorage() 
    {
        return cumulativeStorage;
    }

    public void setUnstoredQuantity(BigDecimal unstoredQuantity) 
    {
        this.unstoredQuantity = unstoredQuantity;
    }

    public BigDecimal getUnstoredQuantity() 
    {
        return unstoredQuantity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("documentDate", getDocumentDate())
            .append("deliveryDate", getDeliveryDate())
            .append("purchaseOrderNo", getPurchaseOrderNo())
            .append("transactionType", getTransactionType())
            .append("supplier", getSupplier())
            .append("documentStatus", getDocumentStatus())
            .append("materialCode", getMaterialCode())
            .append("materialName", getMaterialName())
            .append("specification", getSpecification())
            .append("quantity", getQuantity())
            .append("cumulativeStorage", getCumulativeStorage())
            .append("unstoredQuantity", getUnstoredQuantity())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
