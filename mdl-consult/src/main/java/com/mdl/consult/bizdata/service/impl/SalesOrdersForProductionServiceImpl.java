package com.mdl.consult.bizdata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.SalesOrdersForProductionMapper;
import com.mdl.consult.bizdata.domain.SalesOrdersForProduction;
import com.mdl.consult.bizdata.service.ISalesOrdersForProductionService;
import com.ruoyi.common.core.text.Convert;

/**
 * 需要生产的销售订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class SalesOrdersForProductionServiceImpl implements ISalesOrdersForProductionService
{
    @Autowired
    private SalesOrdersForProductionMapper salesOrdersForProductionMapper;

    /**
     * 查询需要生产的销售订单
     *
     * @param id 需要生产的销售订单主键
     * @return 需要生产的销售订单
     */
    @Override
    public SalesOrdersForProduction selectSalesOrdersForProductionById(Long id)
    {
        return salesOrdersForProductionMapper.selectSalesOrdersForProductionById(id);
    }

    /**
     * 查询需要生产的销售订单列表
     *
     * @param salesOrdersForProduction 需要生产的销售订单
     * @return 需要生产的销售订单
     */
    @Override
    public List<SalesOrdersForProduction> selectSalesOrdersForProductionList(SalesOrdersForProduction salesOrdersForProduction)
    {
        return salesOrdersForProductionMapper.selectSalesOrdersForProductionList(salesOrdersForProduction);
    }

    /**
     * 新增需要生产的销售订单
     *
     * @param salesOrdersForProduction 需要生产的销售订单
     * @return 结果
     */
    @Override
    public int insertSalesOrdersForProduction(SalesOrdersForProduction salesOrdersForProduction)
    {
        salesOrdersForProduction.setCreateTime(DateUtils.getNowDate());
        return salesOrdersForProductionMapper.insertSalesOrdersForProduction(salesOrdersForProduction);
    }

    /**
     * 修改需要生产的销售订单
     *
     * @param salesOrdersForProduction 需要生产的销售订单
     * @return 结果
     */
    @Override
    public int updateSalesOrdersForProduction(SalesOrdersForProduction salesOrdersForProduction)
    {
        salesOrdersForProduction.setUpdateTime(DateUtils.getNowDate());
        return salesOrdersForProductionMapper.updateSalesOrdersForProduction(salesOrdersForProduction);
    }

    /**
     * 批量删除需要生产的销售订单
     *
     * @param ids 需要删除的需要生产的销售订单主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersForProductionByIds(String ids)
    {
        return salesOrdersForProductionMapper.deleteSalesOrdersForProductionByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除需要生产的销售订单信息
     *
     * @param id 需要生产的销售订单主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersForProductionById(Long id)
    {
        return salesOrdersForProductionMapper.deleteSalesOrdersForProductionById(id);
    }

    /**
     * 根据数据分析ID删除需要生产的销售订单数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersForProductionByDataAnalysisId(Long dataAnalysisId)
    {
        return salesOrdersForProductionMapper.deleteSalesOrdersForProductionByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(SalesOrdersForProduction salesOrdersForProduction) {
        BigDecimal monthlyProductionRequired = Objects.requireNonNullElse(salesOrdersForProduction.getSalesOrderQuantity(),new BigDecimal(0))
                .subtract(Objects.requireNonNullElse(salesOrdersForProduction.getLastMonthBalance(),new BigDecimal(0)));

        salesOrdersForProduction.setMonthlyProductionRequired(monthlyProductionRequired);
    }
}
