package com.mdl.consult.bizdata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived;
import com.mdl.consult.bizdata.service.IMaterialsWorkshopReceivedService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 车间已领Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/material/received")
public class MaterialsWorkshopReceivedController extends BaseController
{
    private String prefix = "consult/bizdata/material/received";

    @Autowired
    private IMaterialsWorkshopReceivedService materialsWorkshopReceivedService;

    @RequiresPermissions("consult/bizdata/material:received:view")
    @GetMapping()
    public String received()
    {
        return prefix + "/received";
    }

    /**
     * 查询车间已领列表
     */
    @RequiresPermissions("consult/bizdata/material:received:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        startPage();
        List<MaterialsWorkshopReceived> list = materialsWorkshopReceivedService.selectMaterialsWorkshopReceivedList(materialsWorkshopReceived);
        return getDataTable(list);
    }

    /**
     * 导出车间已领列表
     */
    @RequiresPermissions("consult/bizdata/material:received:export")
    @Log(title = "车间已领", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        List<MaterialsWorkshopReceived> list = materialsWorkshopReceivedService.selectMaterialsWorkshopReceivedList(materialsWorkshopReceived);
        ExcelUtil<MaterialsWorkshopReceived> util = new ExcelUtil<MaterialsWorkshopReceived>(MaterialsWorkshopReceived.class);
        return util.exportExcel(list, "车间已领数据");
    }

    /**
     * 新增车间已领
     */
    @RequiresPermissions("consult/bizdata/material:received:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存车间已领
     */
    @RequiresPermissions("consult/bizdata/material:received:add")
    @Log(title = "车间已领", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        return toAjax(materialsWorkshopReceivedService.insertMaterialsWorkshopReceived(materialsWorkshopReceived));
    }

    /**
     * 修改车间已领
     */
    @RequiresPermissions("consult/bizdata/material:received:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        MaterialsWorkshopReceived materialsWorkshopReceived = materialsWorkshopReceivedService.selectMaterialsWorkshopReceivedById(id);
        mmap.put("materialsWorkshopReceived", materialsWorkshopReceived);
        return prefix + "/edit";
    }

    /**
     * 修改保存车间已领
     */
    @RequiresPermissions("consult/bizdata/material:received:edit")
    @Log(title = "车间已领", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(MaterialsWorkshopReceived materialsWorkshopReceived)
    {
        return toAjax(materialsWorkshopReceivedService.updateMaterialsWorkshopReceived(materialsWorkshopReceived));
    }

    /**
     * 删除车间已领
     */
    @RequiresPermissions("consult/bizdata/material:received:remove")
    @Log(title = "车间已领", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(materialsWorkshopReceivedService.deleteMaterialsWorkshopReceivedByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/material:received:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<MaterialsWorkshopReceived> util = new ExcelUtil<MaterialsWorkshopReceived>(MaterialsWorkshopReceived.class);
        return util.importTemplateExcel("车间已领数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/material:received:import")
    @Log(title = "车间已领", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<MaterialsWorkshopReceived> util = new ExcelUtil<MaterialsWorkshopReceived>(MaterialsWorkshopReceived.class);
        List<MaterialsWorkshopReceived> materialsWorkshopReceivedList = util.importExcel(file.getInputStream());
        String message = importMaterialsWorkshopReceived(dataAnalysisId,materialsWorkshopReceivedList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入车间已领数据
     *
     * @param dataAnalysisId
     * @param materialsWorkshopReceivedList 车间已领数据列表
     * @param isUpdateSupport               是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importMaterialsWorkshopReceived(Long dataAnalysisId, List<MaterialsWorkshopReceived> materialsWorkshopReceivedList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(materialsWorkshopReceivedList) || materialsWorkshopReceivedList.size() == 0)
        {
            throw new ServiceException("导入车间已领数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (MaterialsWorkshopReceived materialsWorkshopReceived : materialsWorkshopReceivedList)
        {
            try
            {
                if(StringUtils.isBlank(materialsWorkshopReceived.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    materialsWorkshopReceived.setDataAnalysisId(dataAnalysisId);
                    materialsWorkshopReceivedService.insertMaterialsWorkshopReceived(materialsWorkshopReceived);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、车间已领 " + materialsWorkshopReceived.getMaterialName() + " 导入成功");
                }
                /* 验证是否存在这个车间已领
                MaterialsWorkshopReceived existReceived = materialsWorkshopReceivedService.selectMaterialsWorkshopReceivedById(materialsWorkshopReceived.getId());
                if (StringUtils.isNull(existReceived))
                {
                    materialsWorkshopReceivedService.insertMaterialsWorkshopReceived(materialsWorkshopReceived);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、车间已领 " + materialsWorkshopReceived.getMaterialName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    materialsWorkshopReceivedService.updateMaterialsWorkshopReceived(materialsWorkshopReceived);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、车间已领 " + materialsWorkshopReceived.getMaterialName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、车间已领 " + materialsWorkshopReceived.getMaterialName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、车间已领 " + materialsWorkshopReceived.getMaterialName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (successNum == 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            if(failureNum > 0){
                successMsg.insert(0, "部分数据导入成功！成功"+successNum+"条，失败"+failureNum+"条，数据如下：");
            }else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
        }

        return successMsg.toString().concat(failureMsg.toString());
    }

    /**
     * 根据数据分析ID删除车间已领数据
     */
    @RequiresPermissions("consult/bizdata/material:received:remove")
    @Log(title = "车间已领", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(materialsWorkshopReceivedService.deleteMaterialsWorkshopReceivedByDataAnalysisId(dataAnalysisId));
    }
}
