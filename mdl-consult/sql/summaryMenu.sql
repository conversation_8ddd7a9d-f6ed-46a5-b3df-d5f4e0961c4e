-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('欠料汇总', '2000', '1', '/consult/summary', 'C', '0', 'consult:summary:view', '#', 'admin', sysdate(), '', null, '欠料汇总菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('欠料汇总查询', @parentId, '1',  '#',  'F', '0', 'consult:summary:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('欠料汇总新增', @parentId, '2',  '#',  'F', '0', 'consult:summary:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('欠料汇总修改', @parentId, '3',  '#',  'F', '0', 'consult:summary:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('欠料汇总删除', @parentId, '4',  '#',  'F', '0', 'consult:summary:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('欠料汇总导出', @parentId, '5',  '#',  'F', '0', 'consult:summary:export',       '#', 'admin', sysdate(), '', null, '');
