-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('车间已领', '2038', '1', '/consult/received', 'C', '0', 'consult:received:view', '#', 'admin', sysdate(), '', null, '车间已领菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('车间已领查询', @parentId, '1',  '#',  'F', '0', 'consult:received:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('车间已领新增', @parentId, '2',  '#',  'F', '0', 'consult:received:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('车间已领修改', @parentId, '3',  '#',  'F', '0', 'consult:received:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('车间已领删除', @parentId, '4',  '#',  'F', '0', 'consult:received:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('车间已领导出', @parentId, '5',  '#',  'F', '0', 'consult:received:export',       '#', 'admin', sysdate(), '', null, '');
