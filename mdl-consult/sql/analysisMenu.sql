-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据分析', '2000', '1', '/consult/analysis', 'C', '0', 'consult:analysis:view', '#', 'admin', sysdate(), '', null, '数据分析菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据分析查询', @parentId, '1',  '#',  'F', '0', 'consult:analysis:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据分析新增', @parentId, '2',  '#',  'F', '0', 'consult:analysis:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据分析修改', @parentId, '3',  '#',  'F', '0', 'consult:analysis:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据分析删除', @parentId, '4',  '#',  'F', '0', 'consult:analysis:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据分析导出', @parentId, '5',  '#',  'F', '0', 'consult:analysis:export',       '#', 'admin', sysdate(), '', null, '');
