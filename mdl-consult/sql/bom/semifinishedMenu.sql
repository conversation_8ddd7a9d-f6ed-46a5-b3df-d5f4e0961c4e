-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('半成品BOM', '2037', '1', '/consult/bom/semifinished', 'C', '0', 'consult:bom/semifinished:view', '#', 'admin', sysdate(), '', null, '半成品BOM菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('半成品BOM查询', @parentId, '1',  '#',  'F', '0', 'consult:bom/semifinished:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('半成品BOM新增', @parentId, '2',  '#',  'F', '0', 'consult:bom/semifinished:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('半成品BOM修改', @parentId, '3',  '#',  'F', '0', 'consult:bom/semifinished:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('半成品BOM删除', @parentId, '4',  '#',  'F', '0', 'consult:bom/semifinished:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('半成品BOM导出', @parentId, '5',  '#',  'F', '0', 'consult:bom/semifinished:export',       '#', 'admin', sysdate(), '', null, '');
