-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品对应的半成品', '2037', '1', '/consult/bom/product-semifinished', 'C', '0', 'consult:bom/product-semifinished:view', '#', 'admin', sysdate(), '', null, '产成品对应的半成品菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品对应的半成品查询', @parentId, '1',  '#',  'F', '0', 'consult:bom/product-semifinished:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品对应的半成品新增', @parentId, '2',  '#',  'F', '0', 'consult:bom/product-semifinished:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品对应的半成品修改', @parentId, '3',  '#',  'F', '0', 'consult:bom/product-semifinished:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品对应的半成品删除', @parentId, '4',  '#',  'F', '0', 'consult:bom/product-semifinished:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品对应的半成品导出', @parentId, '5',  '#',  'F', '0', 'consult:bom/product-semifinished:export',       '#', 'admin', sysdate(), '', null, '');
