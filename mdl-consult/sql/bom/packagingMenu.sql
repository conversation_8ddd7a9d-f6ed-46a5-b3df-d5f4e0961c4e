-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('包装BOM', '2037', '1', '/consult/bom/packaging', 'C', '0', 'consult:bom/packaging:view', '#', 'admin', sysdate(), '', null, '包装BOM菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('包装BOM查询', @parentId, '1',  '#',  'F', '0', 'consult:bom/packaging:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('包装BOM新增', @parentId, '2',  '#',  'F', '0', 'consult:bom/packaging:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('包装BOM修改', @parentId, '3',  '#',  'F', '0', 'consult:bom/packaging:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('包装BOM删除', @parentId, '4',  '#',  'F', '0', 'consult:bom/packaging:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('包装BOM导出', @parentId, '5',  '#',  'F', '0', 'consult:bom/packaging:export',       '#', 'admin', sysdate(), '', null, '');
