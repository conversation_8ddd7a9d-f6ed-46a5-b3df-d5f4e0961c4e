-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成的销售订单（与生产订单核对）', '2000', '1', '/consult/sales-orders/production-check', 'C', '0', 'consult:sales-orders/production-check:view', '#', 'admin', sysdate(), '', null, '未完成的销售订单（与生产订单核对）菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成的销售订单（与生产订单核对）查询', @parentId, '1',  '#',  'F', '0', 'consult:sales-orders/production-check:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成的销售订单（与生产订单核对）新增', @parentId, '2',  '#',  'F', '0', 'consult:sales-orders/production-check:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成的销售订单（与生产订单核对）修改', @parentId, '3',  '#',  'F', '0', 'consult:sales-orders/production-check:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成的销售订单（与生产订单核对）删除', @parentId, '4',  '#',  'F', '0', 'consult:sales-orders/production-check:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成的销售订单（与生产订单核对）导出', @parentId, '5',  '#',  'F', '0', 'consult:sales-orders/production-check:export',       '#', 'admin', sysdate(), '', null, '');
