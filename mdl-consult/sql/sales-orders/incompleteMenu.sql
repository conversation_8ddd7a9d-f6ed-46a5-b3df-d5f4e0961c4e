-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成销售订单列', '2038', '1', '/consult/sales-orders/incomplete', 'C', '0', 'consult:sales-orders/incomplete:view', '#', 'admin', sysdate(), '', null, '未完成销售订单列菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成销售订单列查询', @parentId, '1',  '#',  'F', '0', 'consult:sales-orders/incomplete:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成销售订单列新增', @parentId, '2',  '#',  'F', '0', 'consult:sales-orders/incomplete:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成销售订单列修改', @parentId, '3',  '#',  'F', '0', 'consult:sales-orders/incomplete:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成销售订单列删除', @parentId, '4',  '#',  'F', '0', 'consult:sales-orders/incomplete:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成销售订单列导出', @parentId, '5',  '#',  'F', '0', 'consult:sales-orders/incomplete:export',       '#', 'admin', sysdate(), '', null, '');
