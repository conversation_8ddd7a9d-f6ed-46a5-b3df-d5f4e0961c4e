-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('库存商品数', '2038', '1', '/consult/goods', 'C', '0', 'consult:goods:view', '#', 'admin', sysdate(), '', null, '库存商品数菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('库存商品数查询', @parentId, '1',  '#',  'F', '0', 'consult:goods:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('库存商品数新增', @parentId, '2',  '#',  'F', '0', 'consult:goods:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('库存商品数修改', @parentId, '3',  '#',  'F', '0', 'consult:goods:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('库存商品数删除', @parentId, '4',  '#',  'F', '0', 'consult:goods:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('库存商品数导出', @parentId, '5',  '#',  'F', '0', 'consult:goods:export',       '#', 'admin', sysdate(), '', null, '');
