-- ----------------------------
-- 导入任务表
-- ----------------------------
DROP TABLE IF EXISTS `mdl_import_task`;
CREATE TABLE `mdl_import_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `data_analysis_id` bigint(20) DEFAULT NULL COMMENT '数据分析ID',
  `statistics_number` varchar(50) DEFAULT NULL COMMENT '统计编号',
  `import_type` varchar(50) DEFAULT NULL COMMENT '导入类型',
  `status` char(1) DEFAULT '0' COMMENT '任务状态（0等待中 1导入中 2已完成 3失败）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `success_count` bigint(20) DEFAULT 0 COMMENT '成功数量',
  `failure_count` bigint(20) DEFAULT 0 COMMENT '失败数量',
  `error_message` text COMMENT '错误信息',
  `result_message` text COMMENT '结果信息',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`task_id`),
  KEY `idx_data_analysis_id` (`data_analysis_id`),
  KEY `idx_statistics_number` (`statistics_number`),
  KEY `idx_import_type` (`import_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='导入任务表';
