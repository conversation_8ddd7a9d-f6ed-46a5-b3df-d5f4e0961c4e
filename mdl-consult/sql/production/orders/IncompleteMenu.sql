-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成生产订单', '2038', '1', '/consult/production/orders/Incomplete', 'C', '0', 'consult:production/orders/Incomplete:view', '#', 'admin', sysdate(), '', null, '未完成生产订单菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成生产订单查询', @parentId, '1',  '#',  'F', '0', 'consult:production/orders/Incomplete:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成生产订单新增', @parentId, '2',  '#',  'F', '0', 'consult:production/orders/Incomplete:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成生产订单修改', @parentId, '3',  '#',  'F', '0', 'consult:production/orders/Incomplete:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成生产订单删除', @parentId, '4',  '#',  'F', '0', 'consult:production/orders/Incomplete:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('未完成生产订单导出', @parentId, '5',  '#',  'F', '0', 'consult:production/orders/Incomplete:export',       '#', 'admin', sysdate(), '', null, '');
