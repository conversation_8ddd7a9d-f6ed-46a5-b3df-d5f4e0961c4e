-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('BOM', '2037', '1', '/consult/bom', 'C', '0', 'consult:bom:view', '#', 'admin', sysdate(), '', null, 'BOM菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('BOM查询', @parentId, '1',  '#',  'F', '0', 'consult:bom:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('<PERSON><PERSON>新增', @parentId, '2',  '#',  'F', '0', 'consult:bom:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('BOM修改', @parentId, '3',  '#',  'F', '0', 'consult:bom:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('BOM删除', @parentId, '4',  '#',  'F', '0', 'consult:bom:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('BOM导出', @parentId, '5',  '#',  'F', '0', 'consult:bom:export',       '#', 'admin', sysdate(), '', null, '');
