-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已预购未下采购单', '2038', '1', '/consult/pre-purchased/not-ordered', 'C', '0', 'consult:pre-purchased/not-ordered:view', '#', 'admin', sysdate(), '', null, '已预购未下采购单菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已预购未下采购单查询', @parentId, '1',  '#',  'F', '0', 'consult:pre-purchased/not-ordered:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已预购未下采购单新增', @parentId, '2',  '#',  'F', '0', 'consult:pre-purchased/not-ordered:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已预购未下采购单修改', @parentId, '3',  '#',  'F', '0', 'consult:pre-purchased/not-ordered:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已预购未下采购单删除', @parentId, '4',  '#',  'F', '0', 'consult:pre-purchased/not-ordered:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已预购未下采购单导出', @parentId, '5',  '#',  'F', '0', 'consult:pre-purchased/not-ordered:export',       '#', 'admin', sysdate(), '', null, '');
