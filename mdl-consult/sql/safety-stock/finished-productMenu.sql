-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品安全库存', '2037', '1', '/consult/safety-stock/finished-product', 'C', '0', 'consult:safety-stock/finished-product:view', '#', 'admin', sysdate(), '', null, '产成品安全库存菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品安全库存查询', @parentId, '1',  '#',  'F', '0', 'consult:safety-stock/finished-product:list',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品安全库存新增', @parentId, '2',  '#',  'F', '0', 'consult:safety-stock/finished-product:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品安全库存修改', @parentId, '3',  '#',  'F', '0', 'consult:safety-stock/finished-product:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品安全库存删除', @parentId, '4',  '#',  'F', '0', 'consult:safety-stock/finished-product:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产成品安全库存导出', @parentId, '5',  '#',  'F', '0', 'consult:safety-stock/finished-product:export',       '#', 'admin', sysdate(), '', null, '');
