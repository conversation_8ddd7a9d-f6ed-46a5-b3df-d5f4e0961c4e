FROM registry.cn-hangzhou.aliyuncs.com/yuyidev/openjdk:17-slim
ENV TZ "Asia/Shanghai"
ENV LANG C.UTF-8

# 安装图形库和字体库（关键修复步骤）
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    # X11基础库
    libxext6 \
    libxrender1 \
    libxtst6 \
    # 基础字体库
    fonts-dejavu \
    # 中文字体支持（可选但推荐）
    fonts-wqy-zenhei && \
    # 清理缓存减小镜像体积
    rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制JAR文件
COPY ruoyi-admin.jar ./

# 创建日志目录
RUN mkdir -p logs

# 强制启用无头模式并启动JAR（关键启动参数）
CMD ["java", "-Djava.awt.headless=true", "-jar", "ruoyi-admin.jar"]