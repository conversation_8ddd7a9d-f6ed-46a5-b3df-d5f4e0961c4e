#!/bin/bash
set -e

# 配置参数
PROJECT_ROOT="/Users/<USER>/work/mdl-consult-ruoyi"
DOCKER_DIR="$PROJECT_ROOT/docker"
JAR_SOURCE="$PROJECT_ROOT/ruoyi-admin/target/ruoyi-admin.jar"
REMOTE_DIR="/app/docker"

# 阿里云镜像配置
REGISTRY_USER="wtx062"
REGISTRY_PASSWORD="wtx123456"
REGISTRY_ADDRESS="crpi-p4b4s71qcvrvxxjh.cn-shenzhen.personal.cr.aliyuncs.com"
IMAGE_NAMESPACE="wutx/app"
TAG=$(date +%Y%m%d%H%M%S)
REGISTRY_FULL_NAME="$REGISTRY_ADDRESS/$IMAGE_NAMESPACE"  # 新增合并变量

# Maven构建
echo ">>> 开始Maven构建..."
cd "$PROJECT_ROOT"
mvn clean package -Dmaven.test.skip=true -Pprod

# 准备Docker环境
echo ">>> 准备Docker环境..."
mkdir -p "$DOCKER_DIR"
cp "$JAR_SOURCE" "$DOCKER_DIR"

# 构建镜像
echo ">>> 构建Docker镜像..."
cd "$DOCKER_DIR"
docker build -t "$REGISTRY_FULL_NAME:$TAG" .

# 推送镜像
echo ">>> 登录并推送镜像..."
docker login --username "$REGISTRY_USER" --password "$REGISTRY_PASSWORD" "$REGISTRY_ADDRESS"
docker push "$REGISTRY_FULL_NAME:$TAG"

# 传输文件到服务器
echo ">>> 传输Compose文件..."
scp "$DOCKER_DIR/docker-compose-prod.yml" "mdlserver:$REMOTE_DIR/"

# 远程部署
echo ">>> 远程启动服务..."
ssh mdlserver "cd $REMOTE_DIR && \
echo 'REGISTRY_FULL_NAME=$REGISTRY_FULL_NAME' > .env && \
echo 'TAG=$TAG' >> .env && \
docker login --username $REGISTRY_USER --password $REGISTRY_PASSWORD $REGISTRY_ADDRESS && \
docker compose -f docker-compose-prod.yml up -d"

echo ">>> 部署完成！访问地址：http://mdlserver:9991"