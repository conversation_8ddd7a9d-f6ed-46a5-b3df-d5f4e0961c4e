# 已移除 version 声明（Compose V2+ 不再需要）
services:
  ruoyi-app:
    image: ${REGISTRY_FULL_NAME}:${TAG}  # 合并变量简化引用
    container_name: ruoyi-web
    restart: unless-stopped
    mem_limit: 3072m  # 容器最大可用内存
    mem_reservation: 2048m  # 保证内存
    ports:
      - "9991:9991"
    volumes:
      - /app/logs:/app/logs
      - /app/upload:/app/upload
      - /app/config:/app/config
    environment:
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Xms512m -Xmx2048m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -XX:MaxDirectMemorySize=512m -XX:+UseContainerSupport -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs -Dspring.config.location=/app/config/ -Dlogging.config=/app/config/logback.xml

  mysql:
    image: mysql:8.0  # 官方MySQL镜像(可选5.7/8.0版本)
    container_name: mysql_container
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword  # 强烈建议修改为复杂密码
      MYSQL_DATABASE: mdl-consult            # 容器启动时创建的数据库
      MYSQL_USER: mdl-db-manager              # 自定义用户
      MYSQL_PASSWORD: '#esde@23Sdw@#234Dsdwe#23'      # 用户密码(强烈建议修改)
    volumes:
      - /app/data:/var/lib/mysql             # 数据持久化
      #- ./my.cnf:/etc/mysql/conf.d             # 自定义配置文件(可选)
    ports:
      - "3306:3306"                           # 主机端口:容器端口
    restart: always
    healthcheck:
      test: mysqladmin ping -h localhost -u root -p$$MYSQL_ROOT_PASSWORD
      interval: 5s
      timeout: 5s
      retries: 5