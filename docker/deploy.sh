#!/bin/bash
set -e

# 构建参数
PROJECT_ROOT="/Users/<USER>/work/mdl-consult-ruoyi"
DOCKER_DIR="$PROJECT_ROOT/docker"
JAR_SOURCE="$PROJECT_ROOT/ruoyi-admin/target/ruoyi-admin.jar"

# 执行Maven构建
echo ">>> 开始Maven构建..."
cd "$PROJECT_ROOT"
mvn clean package -Dmaven.test.skip=true -Pprod

# 准备Docker文件
echo ">>> 准备Docker环境..."
mkdir -p "$DOCKER_DIR"
cp "$JAR_SOURCE" "$DOCKER_DIR"

# 构建镜像
echo ">>> 构建Docker镜像..."
cd "$DOCKER_DIR"
docker compose build --no-cache
docker compose up -d

echo ">>> 部署完成！访问地址：http://localhost:8080"