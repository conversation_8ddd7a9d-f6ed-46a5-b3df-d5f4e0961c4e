#!/bin/bash

echo "[信息] 正在重启Web服务..."
echo

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
PARENT_DIR="$(cd "$(dirname "$0")" && pwd)"

#PARENT_DIR="$(dirname "$SCRIPT_DIR")"

# 检查bin目录是否存在，不存在则创建
if [ ! -d "$PARENT_DIR/bin" ]; then
    echo "[信息] bin目录不存在，正在创建..."
    mkdir -p "$PARENT_DIR/bin"
fi

# 检查logs目录是否存在，不存在则创建
if [ ! -d "$PARENT_DIR/logs" ]; then
    echo "[信息] logs目录不存在，正在创建..."
    mkdir -p "$PARENT_DIR/logs"
fi

# 定义变量
PID_FILE="$PARENT_DIR/bin/app.pid"
LOG_FILE="$PARENT_DIR/logs/app.log"

# 停止现有服务
if [ -f "$PID_FILE" ]; then
    pid=$(cat "$PID_FILE")
    if ps -p "$pid" > /dev/null 2>&1; then
        echo "[信息] 正在停止现有服务，PID: $pid"
        kill "$pid"
        sleep 5
        
        # 检查是否还在运行
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "[警告] 服务未能正常停止，尝试强制终止..."
            kill -9 "$pid"
            sleep 2
        fi
    fi
    rm -f "$PID_FILE"
fi

# 检查是否存在ruoyi-admin.jar文件
if [ -f "$PARENT_DIR/ruoyi-admin.jar" ]; then
    echo "[信息] 发现ruoyi-admin.jar文件，正在处理..."
    
    # 清空bin目录下的所有文件
    echo "[信息] 清空bin目录..."
    rm -rf "$PARENT_DIR/bin/"*
    
    # 解压jar文件到bin目录
    echo "[信息] 解压ruoyi-admin.jar到bin目录..."
    cd "$PARENT_DIR"
    unzip -q ruoyi-admin.jar -d "$PARENT_DIR/bin"
    
    # 删除jar文件
    echo "[信息] 删除ruoyi-admin.jar文件..."
    rm -f "$PARENT_DIR/ruoyi-admin.jar"
else
    echo "[信息] 未发现ruoyi-admin.jar文件，跳过解压步骤..."
fi

# 清理旧的日志文件
echo "[信息] 备份原有日志文件..."
if [ -f "$LOG_FILE" ]; then
    mv "$LOG_FILE" "${LOG_FILE}.$(date +%Y%m%d_%H%M%S).bak"
fi

# 启动服务
echo "[信息] 正在启动新的服务实例..."
if [ -f "$PARENT_DIR/bin/BOOT-INF/classes/com/ruoyi/RuoYiApplication.class" ]; then
    # JVM参数
    JAVA_OPTS="-Xms256m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
    
    # 设置类路径
    CLASSPATH="$PARENT_DIR/bin:$PARENT_DIR/bin/BOOT-INF/classes:$PARENT_DIR/bin/BOOT-INF/lib/*"
    
    # 启动应用
    cd "$PARENT_DIR"
    echo "[信息] 使用类路径启动应用..."
    nohup java $JAVA_OPTS -cp "$CLASSPATH" com.ruoyi.RuoYiApplication >> "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    echo "[信息] 应用已在后台启动，PID: $(cat "$PID_FILE")"
else
    echo "[错误] 无法找到RuoYiApplication.class文件，启动失败！"
    exit 1
fi

echo "[信息] 重启完成！"
echo 
