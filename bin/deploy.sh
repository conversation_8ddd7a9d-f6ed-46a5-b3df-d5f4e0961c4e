#!/bin/bash

echo "[信息] 开始部署流程..."
echo

# 获取脚本所在目录
cd "$(dirname "$0")"
cd ..

# 定义变量
PROJECT_ROOT="/Users/<USER>/work/mdl-consult-ruoyi"
TARGET_DIR="$PROJECT_ROOT/ruoyi-admin/target"
REMOTE_SERVER="mdlserver"
REMOTE_PATH="/data/mdl-consult/"
BUILD_LOG="build.log"

# 检查pom.xml是否存在
if [ ! -f "$PROJECT_ROOT/pom.xml" ]; then
    echo "[错误] pom.xml 文件不存在: $PROJECT_ROOT/pom.xml"
    exit 1
fi

# 编译打包
echo "[信息] 开始编译打包..."
mvn clean install -f "$PROJECT_ROOT/pom.xml" > "$BUILD_LOG" 2>&1

# 检查编译结果
if [ $? -ne 0 ]; then
    echo "[错误] 编译失败，请查看 $BUILD_LOG 了解详细信息"
    exit 1
fi
echo "[信息] 编译成功！"

# 检查目标文件
if [ ! -d "$TARGET_DIR" ]; then
    echo "[错误] 编译目标目录不存在: $TARGET_DIR"
    exit 1
fi

# 上传文件到服务器
echo "[信息] 开始上传文件到服务器..."
scp -r "$TARGET_DIR"/ruoyi-admin.jar "$REMOTE_SERVER:$REMOTE_PATH"

# 检查上传结果
if [ $? -ne 0 ]; then
    echo "[错误] 文件上传失败"
    exit 1
fi

echo "[信息] 文件上传成功！"
echo "[信息] 部署完成！"

# 提示重启服务
echo
echo "[提示] 如需重启服务，请在服务器执行以下命令："
echo "      cd $REMOTE_PATH && ./restart.sh"
echo 