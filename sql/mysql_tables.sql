-- MySQL database creation script based on mdl-consult/data.txt
-- Created on: 2023-11-15

-- Create database
CREATE DATABASE IF NOT EXISTS mdl_consult_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mdl_consult_db;

-- Table 0: Data Analysis (数据分析)
CREATE TABLE IF NOT EXISTS mdl_data_analysis (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    statistics_number VARCHAR(50) COMMENT '统计编号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_statistics_number (statistics_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据分析';

-- Table 1: Incomplete sales orders (未完成销售订单列表)
CREATE TABLE IF NOT EXISTS mdl_incomplete_sales_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    order_date DATE COMMENT '订单日期',
    delivery_date DATE COMMENT '订单交期',
    sales_order_no VARCHAR(50) COMMENT '销售单号',
    production_order_no VARCHAR(50) COMMENT '生产单号',
    customer_name VARCHAR(100) COMMENT '客户名称',
    inventory_code VARCHAR(50) COMMENT '存货编码',
    inventory_name VARCHAR(100) COMMENT '存货名称',
    specification VARCHAR(200) COMMENT '规格型号',
    unit VARCHAR(20) COMMENT '单位',
    quantity DECIMAL(10,2) COMMENT '数量',
    cumulative_shipment DECIMAL(10,2) COMMENT '累计发货数量',
    unshipped_quantity DECIMAL(10,2) COMMENT '未发货数量',
    last_month_balance DECIMAL(10,2) COMMENT '上月结存',
    current_month_production DECIMAL(10,2) COMMENT '本月生产入库',
    current_balance DECIMAL(10,2) COMMENT '现在结存',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sales_order (sales_order_no),
    INDEX idx_production_order (production_order_no),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='未完成销售订单列表';

-- Table 2: Incomplete production orders (未完成生产订单列表)
CREATE TABLE IF NOT EXISTS mdl_incomplete_production_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    document_date DATE COMMENT '单据日期',
    production_order_no VARCHAR(50) COMMENT '生产单号',
    customer_name VARCHAR(100) COMMENT '客户名称',
    sales_order_no VARCHAR(50) COMMENT '销售单号',
    material_code VARCHAR(50) COMMENT '物料编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格',
    transaction_type VARCHAR(50) COMMENT '交易类型',
    scheduling_time DATETIME COMMENT '排产时间',
    start_time DATETIME COMMENT '开工时间',
    order_status VARCHAR(20) COMMENT '订单状态',
    production_quantity DECIMAL(10,2) COMMENT '生产数量',
    cumulative_storage DECIMAL(10,2) COMMENT '累计入库数量',
    unstored_quantity DECIMAL(10,2) COMMENT '未入库数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_production_order (production_order_no),
    INDEX idx_sales_order (sales_order_no),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='未完成生产订单列表';

-- Table 3: Sales orders that need production (需要生产的销售订单)
CREATE TABLE IF NOT EXISTS mdl_sales_orders_for_production (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    order_date DATE COMMENT '订单日期',
    delivery_date DATE COMMENT '订单交期',
    sales_order_no VARCHAR(50) COMMENT '销售单号',
    production_order_no VARCHAR(50) COMMENT '生产单号',
    customer_name VARCHAR(100) COMMENT '客户名称',
    inventory_code VARCHAR(50) COMMENT '存货编码',
    inventory_name VARCHAR(100) COMMENT '存货名称',
    specification VARCHAR(200) COMMENT '规格型号',
    unit VARCHAR(20) COMMENT '单位',
    last_month_balance DECIMAL(10,2) COMMENT '上月结存',
    sales_order_quantity DECIMAL(10,2) COMMENT '销售订单数量',
    monthly_production_required DECIMAL(10,2) COMMENT '本月应生产数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sales_order (sales_order_no),
    INDEX idx_production_order (production_order_no),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='需要生产的销售订单';

-- Table 4: Incomplete sales orders (compared with production orders) (未完成的销售订单（与生产订单核对）)
CREATE TABLE IF NOT EXISTS mdl_sales_orders_production_check (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    order_date DATE COMMENT '订单日期',
    order_no VARCHAR(50) COMMENT '单号',
    production_order_no VARCHAR(50) COMMENT '生产单号',
    duplicate_order_no VARCHAR(50) COMMENT '单号(重复字段)',
    customer_code VARCHAR(50) COMMENT '客户编码',
    customer_name VARCHAR(100) COMMENT '客户名称',
    inventory_code VARCHAR(50) COMMENT '存货编码',
    inventory_name VARCHAR(100) COMMENT '存货名称',
    specification VARCHAR(200) COMMENT '规格型号',
    unit VARCHAR(20) COMMENT '单位',
    quantity DECIMAL(10,2) COMMENT '数量',
    cumulative_shipment DECIMAL(10,2) COMMENT '累计发货数量',
    unshipped_quantity DECIMAL(10,2) COMMENT '未发货数量',
    current_inventory DECIMAL(10,2) COMMENT '减现有：库存',
    production_needed DECIMAL(10,2) COMMENT '需要生产数',
    actual_in_production DECIMAL(10,2) COMMENT '实际在产数',
    difference DECIMAL(10,2) COMMENT '差异数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_no (order_no),
    INDEX idx_production_order (production_order_no),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='未完成的销售订单（与生产订单核对）';

-- Table 5: Finished product safety stock (产成品安全库存)
CREATE TABLE IF NOT EXISTS mdl_finished_product_safety_stock (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    inventory_code VARCHAR(50) COMMENT '存货编码',
    inventory_name VARCHAR(100) COMMENT '存货名称',
    specification VARCHAR(200) COMMENT '规格型号',
    unit VARCHAR(20) COMMENT '单位',
    safety_stock DECIMAL(10,2) COMMENT '安全库存',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_inventory_code (inventory_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产成品安全库存';

-- Table 6: Semi-finished products list corresponding to finished products (产成品对应的半成品列表)
CREATE TABLE IF NOT EXISTS mdl_semifinished_products_list (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    neutral_code VARCHAR(50) COMMENT '中性编码',
    quantity DECIMAL(10,2) COMMENT '数量',
    workshop_inventory DECIMAL(10,2) COMMENT '车间仓结存',
    production_required DECIMAL(10,2) COMMENT '应生产',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_neutral_code (neutral_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产成品对应的半成品列表';

-- Table 6b: Semi-finished product components (产成品对应的半成品组件)
CREATE TABLE IF NOT EXISTS mdl_semifinished_product_components (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    semifinished_product_id BIGINT COMMENT '半成品列表ID',
    component_number INT COMMENT '编码序号',
    component_code VARCHAR(50) COMMENT '编码',
    component_quantity DECIMAL(10,2) COMMENT '数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (semifinished_product_id) REFERENCES mdl_semifinished_products_list(id) ON DELETE CASCADE,
    INDEX idx_semifinished_product (semifinished_product_id),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产成品对应的半成品组件';

-- Table 7: BOM table (BOM表)
CREATE TABLE IF NOT EXISTS mdl_bom_table (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    parent_code VARCHAR(50) COMMENT '母件编码',
    bom_level INT COMMENT 'BOM层级',
    material_code VARCHAR(50) COMMENT '物料编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    child_quantity DECIMAL(10,4) COMMENT '子件数量',
    measurement_unit_standard VARCHAR(20) COMMENT '计量单位标准用量',
    main_unit_standard DECIMAL(10,4) COMMENT '主单位标准用量',
    total_parent_quantity DECIMAL(10,2) COMMENT '总母件数量',
    total_child_quantity DECIMAL(10,2) COMMENT '子件总数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_code (parent_code),
    INDEX idx_material_code (material_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM表';

-- Table 8: Semi-finished product BOM (半成品BOM)
CREATE TABLE IF NOT EXISTS mdl_semifinished_product_bom (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    parent_code VARCHAR(50) COMMENT '母件编码',
    parent_name VARCHAR(100) COMMENT '物料名称',
    child_code VARCHAR(50) COMMENT '子件编码',
    child_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    child_quantity DECIMAL(10,4) COMMENT '子件数量',
    child_unit VARCHAR(20) COMMENT '子件计量单位',
    order_quantity DECIMAL(10,2) COMMENT '订单量',
    material_requirement_subtotal DECIMAL(10,2) COMMENT '物料需求小计',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_code (parent_code),
    INDEX idx_child_code (child_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='半成品BOM';

-- Table 9: Packaging BOM (包装BOM)
CREATE TABLE IF NOT EXISTS mdl_packaging_bom (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    parent_code VARCHAR(50) COMMENT '母件编码',
    parent_name VARCHAR(100) COMMENT '物料名称',
    child_code VARCHAR(50) COMMENT '子件编码',
    child_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    child_quantity DECIMAL(10,4) COMMENT '子件数量',
    child_unit VARCHAR(20) COMMENT '子件计量单位',
    order_quantity DECIMAL(10,2) COMMENT '订单量',
    material_requirement_subtotal DECIMAL(10,2) COMMENT '物料需求小计',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_code (parent_code),
    INDEX idx_child_code (child_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='包装BOM';

-- Table 10: Inventory goods (库存商品数)
CREATE TABLE IF NOT EXISTS mdl_inventory_goods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    warehouse VARCHAR(50) COMMENT '仓库',
    material_code VARCHAR(50) COMMENT '物料编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    last_month_balance DECIMAL(10,2) COMMENT '上月结存',
    current_month_inbound DECIMAL(10,2) COMMENT '本月入库',
    current_month_outbound DECIMAL(10,2) COMMENT '本月出库',
    current_month_balance DECIMAL(10,2) COMMENT '本月结存数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_material_code (material_code),
    INDEX idx_warehouse (warehouse),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存商品数';

-- Table 11: Incomplete purchase orders (未完成采购订单列表)
CREATE TABLE IF NOT EXISTS mdl_incomplete_purchase_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    document_date DATE COMMENT '单据日期',
    delivery_date DATE COMMENT '交货期',
    purchase_order_no VARCHAR(50) COMMENT '采购单号',
    transaction_type VARCHAR(50) COMMENT '交易类型',
    supplier VARCHAR(100) COMMENT '供货供应商',
    document_status VARCHAR(20) COMMENT '单据状态',
    material_code VARCHAR(50) COMMENT '物料编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    quantity DECIMAL(10,2) COMMENT '数量',
    cumulative_storage DECIMAL(10,2) COMMENT '累计入库数量',
    unstored_quantity DECIMAL(10,2) COMMENT '未入库数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_purchase_order (purchase_order_no),
    INDEX idx_material_code (material_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='未完成采购订单列表';

-- Table 12: Pre-purchased but not yet ordered (已预购未下采购单)
CREATE TABLE IF NOT EXISTS mdl_pre_purchased_not_ordered (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    pre_purchase_date DATE COMMENT '预购日期',
    pre_purchase_no VARCHAR(50) COMMENT '预购编号',
    supplier VARCHAR(100) COMMENT '供应商',
    purchaser VARCHAR(50) COMMENT '采购人员',
    material_code VARCHAR(50) COMMENT '物料编号',
    product_name VARCHAR(100) COMMENT '产品名称',
    model VARCHAR(200) COMMENT '型号',
    unit VARCHAR(20) COMMENT '单位',
    order_demand_quantity DECIMAL(10,2) COMMENT '订单需求数量',
    purchased_quantity DECIMAL(10,2) COMMENT '已采购数量',
    remaining_purchase_quantity DECIMAL(10,2) COMMENT '剩余采购数量',
    purchase_status VARCHAR(20) COMMENT '采购状态',
    delivery_date DATE COMMENT '交货日期',
    remarks TEXT COMMENT '备注',
    customer_supplied BOOLEAN COMMENT '客供',
    customer_name VARCHAR(100) COMMENT '客户名称',
    pre_purchase_category VARCHAR(50) COMMENT '预购分类',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pre_purchase_no (pre_purchase_no),
    INDEX idx_material_code (material_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已预购未下采购单';

-- Table 13: Incomplete outsourcing orders (未完成委外订单列表)
CREATE TABLE IF NOT EXISTS mdl_incomplete_outsourcing_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    supplier VARCHAR(100) COMMENT '供应商',
    outsourcing_order_no VARCHAR(50) COMMENT '委外单号',
    order_date DATE COMMENT '订单日期',
    delivery_date DATE COMMENT '交货期',
    material_long_code VARCHAR(50) COMMENT '物料长代码',
    material_name VARCHAR(100) COMMENT '物料名称',
    unit VARCHAR(20) COMMENT '单位',
    specification VARCHAR(200) COMMENT '规格型号',
    sent_quantity DECIMAL(10,2) COMMENT '发出数量',
    cumulative_storage DECIMAL(10,2) COMMENT '累计入库数',
    incomplete_storage DECIMAL(10,2) COMMENT '未完成入库数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_outsourcing_order (outsourcing_order_no),
    INDEX idx_material_code (material_long_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='未完成委外订单列表';

-- Table 14: Workshop materials already received (车间已领)
CREATE TABLE IF NOT EXISTS mdl_workshop_received_materials (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    warehouse VARCHAR(50) COMMENT '仓库',
    material_code VARCHAR(50) COMMENT '物料编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    current_quantity DECIMAL(10,2) COMMENT '现存量',
    available_quantity DECIMAL(10,2) COMMENT '可用量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_material_code (material_code),
    INDEX idx_warehouse (warehouse),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车间已领';

-- Table 15: Material safety stock (物料安全库存)
CREATE TABLE IF NOT EXISTS mdl_material_safety_stock (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    warehouse VARCHAR(50) COMMENT '仓库',
    material_code VARCHAR(50) COMMENT '物料编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    safety_stock DECIMAL(10,2) COMMENT '安全库存量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_material_code (material_code),
    INDEX idx_warehouse (warehouse),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料安全库存';

-- Table 16: Material shortage summary (欠料汇总表)
CREATE TABLE IF NOT EXISTS mdl_material_shortage_summary (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_analysis_id BIGINT COMMENT '数据分析ID',
    child_code VARCHAR(50) COMMENT '子件编码',
    material_name VARCHAR(100) COMMENT '物料名称',
    specification VARCHAR(200) COMMENT '规格说明',
    unit VARCHAR(20) COMMENT '单位',
    semifinished_material_requirement DECIMAL(10,2) COMMENT '半成品物料需求小计',
    packaging_material_requirement DECIMAL(10,2) COMMENT '包装物料需求小计',
    order_demand DECIMAL(10,2) COMMENT '订单需求',
    inventory_material DECIMAL(10,2) COMMENT '库存物料',
    purchase_unreturned DECIMAL(10,2) COMMENT '采购未回',
    outsourcing_unreturned DECIMAL(10,2) COMMENT '委外未回',
    workshop_material DECIMAL(10,2) COMMENT '车间物料',
    pre_purchased_not_purchased DECIMAL(10,2) COMMENT '已预购未采购',
    safety_stock DECIMAL(10,2) COMMENT '安全库存',
    shortage DECIMAL(10,2) COMMENT '欠料',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_child_code (child_code),
    INDEX idx_data_analysis (data_analysis_id),
    FOREIGN KEY (data_analysis_id) REFERENCES mdl_data_analysis(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='欠料汇总表';
